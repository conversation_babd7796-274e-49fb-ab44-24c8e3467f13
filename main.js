// main.js - Główny plik aplikacji (moduł)

// Importowanie modułów
import { controls, initControls, previewUpdatingFields } from './config.js';
import { 
    addQuestion, 
    removeQuestion, 
    addAnswer, 
    removeAnswer, 
    toggleManualSizing,
    updateSliderValue,
    toggleConditionalLogic,
    autoResizeTextarea
} from './ui.js';
import { updatePreview } from './preview.js';
import { 
    setupCoreListeners, 
    setupImportListeners, 
    setupGeneratorListeners, 
    setupLoadListeners 
} from './listeners.js';

// --- Inicjalizacja ---

// 1. Zainicjuj obiekty kontrolek
initControls();

// 2. Ustaw nasłuchiwanie na zdarzenia
setupCoreListeners(updatePreview);
setupImportListeners(updatePreview);
setupGeneratorListeners();
setupLoadListeners(updatePreview);

// 3. Włącz domyślnie manualne ustawienia (zgodnie z oryginałem)
controls.manualSizing.checked = true;
toggleManualSizing();

// 4. Dodaj domyślne pytanie na start
// ZMIANA: Użycie nowej sygnatury addQuestion, reszta parametrów (odstępy) weźmie domyślne
addQuestion(
    'Czy to jest najlepszy generator ankiet jaki widziałeś?',
    ['Tak', 'Tak'], 
    false,
    null,
    '1-col',
    0, // questionAnswerGap
    0, // answerSpacing
    50 // NOWOŚĆ: columnWidth
);

// 5. Wygeneruj podgląd po raz pierwszy
updatePreview();

// --- Koniec Inicjalizacji ---