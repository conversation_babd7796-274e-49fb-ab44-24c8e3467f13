// data.js - Funkcje do zbierania, parsowania i ładowania danych

import { controls } from './config.js';
import { addQuestion, toggleManualSizing, updateSliderValue } from './ui.js';
import { updatePreview } from './preview.js';

/**
 * Wypełnia UI na podstawie załadowanych danych
 */
export function populateUi(settings, surveyData) {
    controls.endpointUrl.value = settings.endpointUrl;
    controls.exitUrl.value = settings.exitUrl;
    
    const validBannerSize = `${settings.bannerWidth}x${settings.bannerHeight}`;
    if (controls.bannerSize.querySelector(`option[value="${validBannerSize}"]`)) {
         controls.bannerSize.value = validBannerSize;
    } else {
         controls.bannerSize.value = '300x250';
    }
   
    // NOWOŚĆ: Logika wypełniania dla połączonej listy fontów
    if (settings.fontPackageName) {
        // Jeśli mamy paczk<PERSON> (np. "lato"), wybierz ją
        controls.fontSelection.value = settings.fontPackageName;
    } else if (settings.fontFamily) {
        // W przeciwnym razie wybierz font bezpieczny (np. "Arial, sans-serif")
        controls.fontSelection.value = settings.fontFamily;
    } else {
        controls.fontSelection.value = 'Arial, sans-serif'; // Domyślny
    }
    
    controls.backgroundColor.value = settings.backgroundColor || '#ffffff';
    controls.backgroundColorText.value = settings.backgroundColor || '#ffffff';
    controls.answerHoverColor.value = settings.answerHoverColor || '#00FFFF';
    controls.answerHoverColorText.value = settings.answerHoverColor || '#00FFFF';
    
    controls.headerBgColor.value = settings.headerBgColor || '#000000';
    controls.headerBgColorText.value = settings.headerBgColor || '#000000';
    controls.headerFontColor.value = settings.headerFontColor || '#ffffff';
    controls.headerFontColorText.value = settings.headerFontColor || '#ffffff';
    controls.questionFontColor.value = settings.questionFontColor || '#1e293b';
    controls.questionFontColorText.value = settings.questionFontColor || '#1e293b';
    controls.answerFontColor.value = settings.answerFontColor || '#334155';
    controls.answerFontColorText.value = settings.answerFontColor || '#334155';
    controls.radioBgColor.value = settings.radioBgColor || '#ffffff';
    controls.radioBgColorText.value = settings.radioBgColor || '#ffffff';
    controls.radioBorderColor.value = settings.radioBorderColor || '#64748b';
    controls.radioBorderColorText.value = settings.radioBorderColor || '#64748b';
    controls.radioDotColor.value = settings.radioDotColor || '#3b82f6';
    controls.radioDotColorText.value = settings.radioDotColor || '#3b82f6';

    controls.manualSizing.checked = settings.manualSizing || false;
    toggleManualSizing();

    if (settings.manualSizing) {
        const slidersToUpdate = [
            ['header-text-size', settings.headerTextSize, 12],
            ['header-padding', settings.headerPadding, 8],
            ['question-font-size', settings.questionFontSize, 18],
            ['answer-font-size', settings.answerFontSize, 14],
            ['radio-size', settings.radioSize, 18],
            ['spacing-size', settings.spacingSize, 12],
            ['padding-size', settings.paddingSize, 20],
            ['content-top-padding', settings.contentTopPadding, 20],
            ['global-question-answer-gap', settings.globalQuestionAnswerGap, 5],
            ['thankyou-title-size', settings.thankYouTitleSize, 24],
            ['thankyou-message-size', settings.thankYouMessageSize, 16],
            ['welcome-title-size', settings.welcomeTitleSize, 24], // DODANE
            ['welcome-button-font-size', settings.welcomeButtonFontSize, 16], // Zmienione z controls
            ['welcome-button-padding', settings.welcomeButtonPadding, 10] // Zmienione z controls
        ];

        slidersToUpdate.forEach(([controlId, value, defaultValue]) => {
            const slider = document.getElementById(controlId);
            if (slider) {
                slider.value = value || defaultValue;
                updateSliderValue(controlId, slider.value); 
            }
        });
    }
    
    controls.thankYouTitle.value = settings.thankYouTitle ?? 'DZIĘKUJEMY.';
    controls.thankYouMessage.value = settings.thankYouMessage ?? 'TWOJA ODPOWIEDŹ ZAPISANA.';

    // Ustawienia ekranu powitalnego
    controls.welcomeTitle.value = settings.welcomeTitle ?? 'Pomóż nam zadbać o Twoje potrzeby';
    controls.welcomeButtonText.value = settings.welcomeButtonText ?? 'Wypełnij ankietę';
    // Rozmiary są ustawiane w bloku slidersToUpdate
    
    // Ustawienia kolorów przycisku powitalnego
    controls.welcomeButtonBgColor.value = settings.welcomeButtonBg ?? '#000000';
    controls.welcomeButtonBgColorText.value = settings.welcomeButtonBg ?? '#000000';
    controls.welcomeButtonTextColor.value = settings.welcomeButtonText ?? '#ffffff';
    controls.welcomeButtonTextColorText.value = settings.welcomeButtonText ?? '#ffffff';
    controls.welcomeButtonHoverBgColor.value = settings.welcomeButtonHoverBg ?? '#333333';
    controls.welcomeButtonHoverBgColorText.value = settings.welcomeButtonHoverBg ?? '#333333';
    
    controls.questionNumbering.checked = settings.questionNumbering ?? true;

    // Stwórz pytania
    controls.questionsContainer.innerHTML = ''; // Wyczyść przed dodaniem
    surveyData.questions.forEach(q => {
        addQuestion(
            q.text.replace(/<br>/g, '\n'), 
            q.answers.map(a => a.replace(/<br>/g, '\n')), 
            q.randomize, 
            q.conditional,
            q.layout,
            q.questionAnswerGap,
            q.answerSpacing,
            q.columnWidth || 50
        );
    });
    
    updatePreview();
}

/**
 * Zbiera wszystkie dane z UI do jednego obiektu
 */
export function collectDataFromUI() {
    const selectedOption = controls.bannerSize.selectedOptions[0];
    const bannerWidth = selectedOption.dataset.width;
    const bannerHeight = selectedOption.dataset.height;
    
    // --- NOWA LOGIKA ZBIERANIA FONTÓW ---
    const fontSelectedOption = controls.fontSelection.selectedOptions[0];
    const fontValue = fontSelectedOption.value;
    const fontCssName = fontSelectedOption.dataset.fontName; // Będzie istniał only dla @fontsource

    let fontPackageName = '';
    let fontFamily = fontValue; // Domyślnie (np. "Arial, sans-serif")

    if (fontCssName) {
        // Jeśli to font @fontsource...
        fontPackageName = fontValue; // Wartością jest nazwa paczki (np. "lato")
        // Użyj 'Arial, sans-serif' jako twardego fallbacka
        fontFamily = `'${fontCssName}', Arial, sans-serif`; 
    }
    // --- KONIEC NOWEJ LOGIKI ---

    const manualSizing = controls.manualSizing.checked;
    const backgroundColor = controls.backgroundColor.value;
    const answerHoverColor = controls.answerHoverColor.value;

    const settings = {
        endpointUrl: controls.endpointUrl.value,
        exitUrl: controls.exitUrl.value,
        bannerWidth: bannerWidth,
        bannerHeight: bannerHeight,
        fontPackageName: fontPackageName,
        fontFamily: fontFamily,
        backgroundColor: backgroundColor,
        answerHoverColor: answerHoverColor,
        manualSizing: manualSizing,
        headerTextSize: controls.headerTextSize.value,
        headerPadding: controls.headerPadding.value,
        questionFontSize: controls.questionFontSize.value,
        answerFontSize: controls.answerFontSize.value,
        radioSize: controls.radioSize.value,
        spacingSize: controls.spacingSize.value,
        paddingSize: controls.paddingSize.value,
        contentTopPadding: controls.contentTopPadding.value,
        globalQuestionAnswerGap: controls.globalQuestionAnswerGap.value,
        thankYouTitleSize: controls.thankYouTitleSize.value,
        thankYouMessageSize: controls.thankYouMessageSize.value,
        thankYouTitle: controls.thankYouTitle.value,
        thankYouMessage: controls.thankYouMessage.value,
        
        // Ustawienia ekranu powitalnego
        welcomeTitle: controls.welcomeTitle.value,
        welcomeTitleSize: controls.welcomeTitleSize.value, // DODANE
        welcomeButtonText: controls.welcomeButtonText.value,
        welcomeButtonFontSize: controls.welcomeButtonFontSize.value,
        welcomeButtonPadding: controls.welcomeButtonPadding.value,
        welcomeButtonBg: controls.welcomeButtonBgColor.value, // DODANE
        welcomeButtonText: controls.welcomeButtonTextColor.value, // DODANE
        welcomeButtonHoverBg: controls.welcomeButtonHoverBgColor.value, // DODANE
        
        questionNumbering: controls.questionNumbering.checked,

        headerBgColor: controls.headerBgColor.value,
        headerFontColor: controls.headerFontColor.value,
        questionFontColor: controls.questionFontColor.value,
        answerFontColor: controls.answerFontColor.value,
        radioBgColor: controls.radioBgColor.value,
        radioBorderColor: controls.radioBorderColor.value,
        radioDotColor: controls.radioDotColor.value
    };

    const questions = [];
    const questionCards = controls.questionsContainer.querySelectorAll('[data-question-id]');
    
    questionCards.forEach(card => {
        const questionText = card.querySelector('.question-text').value.trim().replace(/\n/g, '<br>');
        const answerInputs = card.querySelectorAll('.answer-text');
        const randomize = card.querySelector('.randomize-answers').checked;
        const layout = card.querySelector('.answers-layout').value;
        const questionAnswerGap = card.querySelector('.question-answer-gap-slider').value;
        const answerSpacing = card.querySelector('.answer-spacing-slider').value;
        const columnWidth = card.querySelector('.column-width-slider').value;
        
        const conditionalEnabled = card.querySelector('.conditional-enabled').checked;
        let conditional = null;
        
        if (conditionalEnabled) {
            const refQuestionId = card.querySelector('.conditional-question-ref').value;
            const refAnswer = card.querySelector('.conditional-answer-ref').value;
            
            if (refQuestionId && refAnswer) {
                conditional = {
                    questionId: parseInt(refQuestionId),
                    answer: refAnswer
                };
            }
        }
        
        const answers = Array.from(answerInputs)
            .map(input => input.value.trim().replace(/\n/g, '<br>'))
            .filter(text => text.length > 0);
        
        if (questionText && answers.length > 0) {
            questions.push({
                text: questionText,
                answers: answers,
                randomize: randomize,
                conditional: conditional,
                layout: layout,
                questionAnswerGap: questionAnswerGap,
                answerSpacing: answerSpacing,
                columnWidth: columnWidth
            });
        }
    });

    return {
        settings,
        surveyData: { questions }
    };
}

/**
 * Parsuje pytania z bloku tekstu
 */
export function parseQuestionsFromText(text) {
    const questions = [];
    const lines = text.split('\n').map(line => line.trim());
    
    let currentQuestion = null;
    let currentAnswers = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        if (line === '') {
            if (currentQuestion && currentAnswers.length > 0) {
                questions.push({
                    text: currentQuestion,
                    answers: currentAnswers
                });
                currentQuestion = null;
                currentAnswers = [];
            }
            continue;
        }
        
        const questionMatch = line.match(/^(\d+)[.)]\s*(.+)$/);
        if (questionMatch) {
            if (currentQuestion && currentAnswers.length > 0) {
                questions.push({
                    text: currentQuestion,
                    answers: currentAnswers
                });
            }
            
            currentQuestion = questionMatch[2];
            currentAnswers = [];
            continue;
        }
        
        if (currentQuestion && line.length > 0) {
            currentAnswers.push(line);
        }
    }
    
    if (currentQuestion && currentAnswers.length > 0) {
        questions.push({
            text: currentQuestion,
            answers: currentAnswers
        });
    }
    
    return questions;
}