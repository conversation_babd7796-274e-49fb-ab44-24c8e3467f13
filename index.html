<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>/// GENERATOR BANERÓW ANKIETOWYCH v.2.0 BRUTALIST ///</title>
    <!-- Tailwind CSS --><script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Font --><link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700;900&display=swap" rel="stylesheet">

    <!-- Przeniesione style -->
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-[var(--color-bg-light)] flex flex-col h-screen">

    <!-- Nagłówek Aplikacji -->
    <header class="w-full p-4 flex flex-col items-center gap-4 md:flex-row md:justify-between md:items-center z-10 sticky top-0 brutalist-header flex-shrink-0">
        <h1 class="text-3xl font-black brutalist-title text-black text-center md:text-left">
            // SURVEY.BANNER.GEN
        </h1>
        <div class="flex flex-col sm:flex-row gap-3">
            <input type="file" id="load-banner-input" class="hidden" accept=".html">
            <button id="load-banner-btn" class="brutalist-btn brutalist-btn-secondary text-sm">
                ZAŁADUJ
            </button>
            <button id="generate-banner-btn" class="brutalist-btn brutalist-btn-primary text-sm">
                >>> GENERUJ PLIK HTML
            </button>
        </div>
    </header>

    <!-- 
      ZMIANA (Z-INDEX): Dodano 'relative' i 'z-0', aby ten kontener (i jego scrollbary)
      renderował się *pod* nagłówkiem (który ma z-10).
    -->
    <main class="relative z-0 flex-grow flex flex-col lg:flex-row overflow-y-auto lg:overflow-hidden">

        <!-- 
          LEWA STRONA: 
          - 'w-full' (mobilny) - panel zajmuje całą szerokość.
          - 'lg:w-1/3' (desktop) - panel zajmuje 1/3 szerokości.
          - 'lg:overflow-y-auto' - przewijanie jest aktywne only na desktopie.
        -->
        <div id="controls-panel" class="flex flex-col gap-6 w-full lg:w-1/3 pt-6 pb-6 pl-6 pr-4 flex-shrink-0 lg:overflow-y-auto">
            
            <!-- 1. Ustawienia Ogólne --><div class="brutalist-card p-5">
                <h2 class="text-xl font-bold brutalist-title border-b border-black pb-3 mb-4">
                    [01] KONFIGURACJA ŚRODOWISKA
                </h2>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="endpoint-url" class="block text-xs font-bold mb-1">URL ENDPOINT (POST)</label>
                        <input type="text" id="endpoint-url" class="w-full brutalist-input" value="https://xxx.com/xxx.php">
                    </div>
                    <div>
                        <label for="exit-url" class="block text-xs font-bold mb-1">EXIT URL (CLICKTAG)</label>
                        <input type="text" id="exit-url" class="w-full brutalist-input" value="https://www.google.pl/">
                    </div>
                    
                    <div class="config-group"> 
                        <div class="config-item select-wrapper">
                            <label for="banner-size" class="block text-xs font-bold mb-1">FORMAT</label>
                            <select id="banner-size" class="w-full brutalist-input brutalist-select">
                                <option value="300x250" data-width="300" data-height="250" selected>300 x 250 (Desktop/M)</option>
                                <option value="300x600" data-width="300" data-height="600">300 x 600 (Skyscraper)</option>
                                <option value="320x480" data-width="320" data-height="480">320 x 480 (Mobile Full)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- [01B] Style Banera --><div class="brutalist-card p-5">
                <h2 class="text-xl font-bold brutalist-title border-b border-black pb-3 mb-4">
                    [01B] STYL BANERA (KOLORY I FONT)
                </h2>
                
                <div class="config-group"> 
                    <!-- Kolor Tła Nagłówka -->
                    <div class="config-item">
                        <label for="header-bg-color-text" class="block text-xs font-bold mb-1">TŁO NAGŁÓWKA</label>
                        <div class="flex gap-2">
                            <input type="color" id="header-bg-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#000000">
                            <input type="text" id="header-bg-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#000000" placeholder="#000000">
                        </div>
                    </div>
                    
                    <!-- Kolor Fontu Nagłówka -->
                    <div class="config-item">
                        <label for="header-font-color-text" class="block text-xs font-bold mb-1">FONT NAGŁÓWKA</label>
                        <div class="flex gap-2">
                            <input type="color" id="header-font-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#ffffff">
                            <input type="text" id="header-font-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#ffffff" placeholder="#FFFFFF">
                        </div>
                    </div>

                    <!-- Kolor Fontu Pytania -->
                    <div class="config-item">
                        <label for="question-font-color-text" class="block text-xs font-bold mb-1">FONT PYTANIA</label>
                        <div class="flex gap-2">
                            <input type="color" id="question-font-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#000000">
                            <input type="text" id="question-font-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#000000" placeholder="#000000">
                        </div>
                    </div>

                    <!-- Kolor Fontu Odpowiedzi -->
                    <div class="config-item">
                        <label for="answer-font-color-text" class="block text-xs font-bold mb-1">FONT ODPOWIEDZI</label>
                        <div class="flex gap-2">
                            <input type="color" id="answer-font-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#000000">
                            <input type="text" id="answer-font-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#000000" placeholder="#000000">
                        </div>
                    </div>

                    <!-- Tło Radio -->
                    <div class="config-item">
                        <label for="radio-bg-color-text" class="block text-xs font-bold mb-1">TŁO RADIO</label>
                        <div class="flex gap-2">
                            <input type="color" id="radio-bg-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#ffffff">
                            <input type="text" id="radio-bg-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#ffffff" placeholder="#FFFFFF">
                        </div>
                    </div>

                    <!-- Border Radio -->
                    <div class="config-item">
                        <label for="radio-border-color-text" class="block text-xs font-bold mb-1">BORDER RADIO</label>
                        <div class="flex gap-2">
                            <input type="color" id="radio-border-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#000000">
                            <input type="text" id="radio-border-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#000000" placeholder="#000000">
                        </div>
                    </div>

                    <!-- Kropka Radio (Aktywny) -->
                    <div class="config-item">
                        <label for="radio-dot-color-text" class="block text-xs font-bold mb-1">KROPKA RADIO (AKTYWNY)</label>
                        <div class="flex gap-2">
                            <input type="color" id="radio-dot-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#000000">
                            <input type="text" id="radio-dot-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#000000" placeholder="#000000">
                        </div>
                    </div>

                    <!-- Kolor Tła Banera -->
                    <div class="config-item">
                        <label for="background-color-text" class="block text-xs font-bold mb-1">KOLOR TŁA BANERA</A></label>
                        <div class="flex gap-2">
                            <input type="color" id="background-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#e7e8f3">
                            <input type="text" id="background-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#e7e8f3" placeholder="#E7E8F3">
                        </div>
                    </div>
                    
                    <!-- Kolor Hover Odpowiedzi -->
                    <div class="config-item">
                        <label for="answer-hover-color-text" class="block text-xs font-bold mb-1">KOLOR HOVER ODPOWIEDZI</label>
                        <div class="flex gap-2">
                            <input type="color" id="answer-hover-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#ffffff">
                            <input type="text" id="answer-hover-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#ffffff" placeholder="#FFFFFF">
                        </div>
                    </div>

                    <!-- ZMIANA: Połączona lista fontów -->
                    <div class="config-item select-wrapper" style="grid-column: 1 / -1;">
                        <label for="font-selection" class="block text-xs font-bold mb-1">WYBÓR CZCIONKI (SAFE LUB @FONTSOURCE)</label>
                        <select id="font-selection" class="w-full brutalist-input brutalist-select">
                            <optgroup label="Czcionki Bezpieczne (DCM Safe)">
                                <option value="Arial, sans-serif" selected>Arial (DCM Safe)</option>
                                <option value="Verdana, sans-serif">Verdana (DCM Safe)</option>
                                <option value="Tahoma, sans-serif">Tahoma (DCM Safe)</option>
                                <option value="'Trebuchet MS', sans-serif">Trebuchet MS (DCM Safe)</option>
                                <option value="sans-serif">System Sans-serif (Fallback)</option>
                                <option value="monospace">System Monospace (Fallback)</option>
                            </optgroup>
                            <option value="42dot-sans" data-font-name="42dot Sans">42dot Sans</option>
<option value="abeezee" data-font-name="ABeeZee">ABeeZee</option>
<option value="abel" data-font-name="Abel">Abel</option>
<option value="abhaya-libre" data-font-name="Abhaya Libre">Abhaya Libre</option>
<option value="aboreto" data-font-name="Aboreto">Aboreto</option>
<option value="abril-fatface" data-font-name="Abril Fatface">Abril Fatface</option>
<option value="abyssinica-sil" data-font-name="Abyssinica SIL">Abyssinica SIL</option>
<option value="aclonica" data-font-name="Aclonica">Aclonica</option>
<option value="acme" data-font-name="Acme">Acme</option>
<option value="actor" data-font-name="Actor">Actor</option>
<option value="adamina" data-font-name="Adamina">Adamina</option>
<option value="adlam-display" data-font-name="ADLaM Display">ADLaM Display</option>
<option value="advent-pro" data-font-name="Advent Pro">Advent Pro</option>
<option value="afacad" data-font-name="Afacad">Afacad</option>
<option value="afacad-flux" data-font-name="Afacad Flux">Afacad Flux</option>
<option value="agbalumo" data-font-name="Agbalumo">Agbalumo</option>
<option value="agdasima" data-font-name="Agdasima">Agdasima</option>
<option value="agu-display" data-font-name="Agu Display">Agu Display</option>
<option value="aguafina-script" data-font-name="Aguafina Script">Aguafina Script</option>
<option value="akatab" data-font-name="Akatab">Akatab</option>
<option value="akaya-kanadaka" data-font-name="Akaya Kanadaka">Akaya Kanadaka</option>
<option value="akaya-telivigala" data-font-name="Akaya Telivigala">Akaya Telivigala</option>
<option value="akronim" data-font-name="Akronim">Akronim</option>
<option value="akshar" data-font-name="Akshar">Akshar</option>
<option value="aladin" data-font-name="Aladin">Aladin</option>
<option value="alan-sans" data-font-name="Alan Sans">Alan Sans</option>
<option value="alata" data-font-name="Alata">Alata</option>
<option value="alatsi" data-font-name="Alatsi">Alatsi</option>
<option value="albert-sans" data-font-name="Albert Sans">Albert Sans</option>
<option value="aldrich" data-font-name="Aldrich">Aldrich</option>
<option value="alef" data-font-name="Alef">Alef</option>
<option value="alegreya" data-font-name="Alegreya">Alegreya</option>
<option value="alegreya-sans" data-font-name="Alegreya Sans">Alegreya Sans</option>
<option value="alegreya-sans-sc" data-font-name="Alegreya Sans SC">Alegreya Sans SC</option>
<option value="alegreya-sc" data-font-name="Alegreya SC">Alegreya SC</option>
<option value="aleo" data-font-name="Aleo">Aleo</option>
<option value="alex-brush" data-font-name="Alex Brush">Alex Brush</option>
<option value="alexandria" data-font-name="Alexandria">Alexandria</option>
<option value="alfa-slab-one" data-font-name="Alfa Slab One">Alfa Slab One</option>
<option value="alice" data-font-name="Alice">Alice</option>
<option value="alike" data-font-name="Alike">Alike</option>
<option value="alike-angular" data-font-name="Alike Angular">Alike Angular</option>
<option value="alkalami" data-font-name="Alkalami">Alkalami</option>
<option value="alkatra" data-font-name="Alkatra">Alkatra</option>
<option value="allan" data-font-name="Allan">Allan</option>
<option value="allerta" data-font-name="Allerta">Allerta</option>
<option value="allerta-stencil" data-font-name="Allerta Stencil">Allerta Stencil</option>
<option value="allison" data-font-name="Allison">Allison</option>
<option value="allura" data-font-name="Allura">Allura</option>
<option value="almarai" data-font-name="Almarai">Almarai</option>
<option value="almendra" data-font-name="Almendra">Almendra</option>
<option value="almendra-display" data-font-name="Almendra Display">Almendra Display</option>
<option value="almendra-sc" data-font-name="Almendra SC">Almendra SC</option>
<option value="alumni-sans" data-font-name="Alumni Sans">Alumni Sans</option>
<option value="alumni-sans-collegiate-one" data-font-name="Alumni Sans Collegiate One">Alumni Sans Collegiate One</option>
<option value="alumni-sans-inline-one" data-font-name="Alumni Sans Inline One">Alumni Sans Inline One</option>
<option value="alumni-sans-pinstripe" data-font-name="Alumni Sans Pinstripe">Alumni Sans Pinstripe</option>
<option value="alumni-sans-sc" data-font-name="Alumni Sans SC">Alumni Sans SC</option>
<option value="amarante" data-font-name="Amarante">Amarante</option>
<option value="amaranth" data-font-name="Amaranth">Amaranth</option>
<option value="amatic-sc" data-font-name="Amatic SC">Amatic SC</option>
<option value="amethysta" data-font-name="Amethysta">Amethysta</option>
<option value="amiko" data-font-name="Amiko">Amiko</option>
<option value="amiri" data-font-name="Amiri">Amiri</option>
<option value="amiri-quran" data-font-name="Amiri Quran">Amiri Quran</option>
<option value="amita" data-font-name="Amita">Amita</option>
<option value="anaheim" data-font-name="Anaheim">Anaheim</option>
<option value="ancizar-sans" data-font-name="Ancizar Sans">Ancizar Sans</option>
<option value="ancizar-serif" data-font-name="Ancizar Serif">Ancizar Serif</option>
<option value="andada-pro" data-font-name="Andada Pro">Andada Pro</option>
<option value="andika" data-font-name="Andika">Andika</option>
<option value="anek-bangla" data-font-name="Anek Bangla">Anek Bangla</option>
<option value="anek-devanagari" data-font-name="Anek Devanagari">Anek Devanagari</option>
<option value="anek-gujarati" data-font-name="Anek Gujarati">Anek Gujarati</option>
<option value="anek-gurmukhi" data-font-name="Anek Gurmukhi">Anek Gurmukhi</option>
<option value="anek-kannada" data-font-name="Anek Kannada">Anek Kannada</option>
<option value="anek-latin" data-font-name="Anek Latin">Anek Latin</option>
<option value="anek-malayalam" data-font-name="Anek Malayalam">Anek Malayalam</option>
<option value="anek-odia" data-font-name="Anek Odia">Anek Odia</option>
<option value="anek-tamil" data-font-name="Anek Tamil">Anek Tamil</option>
<option value="anek-telugu" data-font-name="Anek Telugu">Anek Telugu</option>
<option value="angkor" data-font-name="Angkor">Angkor</option>
<option value="annapurna-sil" data-font-name="Annapurna SIL">Annapurna SIL</option>
<option value="annie-use-your-telescope" data-font-name="Annie Use Your Telescope">Annie Use Your Telescope</option>
<option value="anonymous-pro" data-font-name="Anonymous Pro">Anonymous Pro</option>
<option value="anta" data-font-name="Anta">Anta</option>
<option value="antic" data-font-name="Antic">Antic</option>
<option value="antic-didone" data-font-name="Antic Didone">Antic Didone</option>
<option value="antic-slab" data-font-name="Antic Slab">Antic Slab</option>
<option value="anton" data-font-name="Anton">Anton</option>
<option value="anton-sc" data-font-name="Anton SC">Anton SC</option>
<option value="antonio" data-font-name="Antonio">Antonio</option>
<option value="anuphan" data-font-name="Anuphan">Anuphan</option>
<option value="anybody" data-font-name="Anybody">Anybody</option>
<option value="aoboshi-one" data-font-name="Aoboshi One">Aoboshi One</option>
<option value="ar-one-sans" data-font-name="AR One Sans">AR One Sans</option>
<option value="arapey" data-font-name="Arapey">Arapey</option>
<option value="arbutus" data-font-name="Arbutus">Arbutus</option>
<option value="arbutus-slab" data-font-name="Arbutus Slab">Arbutus Slab</option>
<option value="architects-daughter" data-font-name="Architects Daughter">Architects Daughter</option>
<option value="archivo" data-font-name="Archivo">Archivo</option>
<option value="archivo-black" data-font-name="Archivo Black">Archivo Black</option>
<option value="archivo-narrow" data-font-name="Archivo Narrow">Archivo Narrow</option>
<option value="are-you-serious" data-font-name="Are You Serious">Are You Serious</option>
<option value="aref-ruqaa" data-font-name="Aref Ruqaa">Aref Ruqaa</option>
<option value="aref-ruqaa-ink" data-font-name="Aref Ruqaa Ink">Aref Ruqaa Ink</option>
<option value="arima" data-font-name="Arima">Arima</option>
<option value="arima-madurai" data-font-name="Arima Madurai">Arima Madurai</option>
<option value="arimo" data-font-name="Arimo">Arimo</option>
<option value="arizonia" data-font-name="Arizonia">Arizonia</option>
<option value="armata" data-font-name="Armata">Armata</option>
<option value="arsenal" data-font-name="Arsenal">Arsenal</option>
<option value="arsenal-sc" data-font-name="Arsenal SC">Arsenal SC</option>
<option value="artifika" data-font-name="Artifika">Artifika</option>
<option value="arvo" data-font-name="Arvo">Arvo</option>
<option value="arya" data-font-name="Arya">Arya</option>
<option value="asap" data-font-name="Asap">Asap</option>
<option value="asap-condensed" data-font-name="Asap Condensed">Asap Condensed</option>
<option value="asar" data-font-name="Asar">Asar</option>
<option value="asimovian" data-font-name="Asimovian">Asimovian</option>
<option value="asset" data-font-name="Asset">Asset</option>
<option value="assistant" data-font-name="Assistant">Assistant</option>
<option value="asta-sans" data-font-name="Asta Sans">Asta Sans</option>
<option value="astloch" data-font-name="Astloch">Astloch</option>
<option value="asul" data-font-name="Asul">Asul</option>
<option value="athiti" data-font-name="Athiti">Athiti</option>
<option value="atkinson-hyperlegible" data-font-name="Atkinson Hyperlegible">Atkinson Hyperlegible</option>
<option value="atkinson-hyperlegible-mono" data-font-name="Atkinson Hyperlegible Mono">Atkinson Hyperlegible Mono</option>
<option value="atkinson-hyperlegible-next" data-font-name="Atkinson Hyperlegible Next">Atkinson Hyperlegible Next</option>
<option value="atma" data-font-name="Atma">Atma</option>
<option value="atomic-age" data-font-name="Atomic Age">Atomic Age</option>
<option value="aubrey" data-font-name="Aubrey">Aubrey</option>
<option value="audiowide" data-font-name="Audiowide">Audiowide</option>
<option value="autour-one" data-font-name="Autour One">Autour One</option>
<option value="average" data-font-name="Average">Average</option>
<option value="average-sans" data-font-name="Average Sans">Average Sans</option>
<option value="averia-gruesa-libre" data-font-name="Averia Gruesa Libre">Averia Gruesa Libre</option>
<option value="averia-libre" data-font-name="Averia Libre">Averia Libre</option>
<option value="averia-sans-libre" data-font-name="Averia Sans Libre">Averia Sans Libre</option>
<option value="averia-serif-libre" data-font-name="Averia Serif Libre">Averia Serif Libre</option>
<option value="azeret-mono" data-font-name="Azeret Mono">Azeret Mono</option>
<option value="b612" data-font-name="B612">B612</option>
<option value="b612-mono" data-font-name="B612 Mono">B612 Mono</option>
<option value="babylonica" data-font-name="Babylonica">Babylonica</option>
<option value="bacasime-antique" data-font-name="Bacasime Antique">Bacasime Antique</option>
<option value="bad-script" data-font-name="Bad Script">Bad Script</option>
<option value="badeen-display" data-font-name="Badeen Display">Badeen Display</option>
<option value="bagel-fat-one" data-font-name="Bagel Fat One">Bagel Fat One</option>
<option value="bahiana" data-font-name="Bahiana">Bahiana</option>
<option value="bahianita" data-font-name="Bahianita">Bahianita</option>
<option value="bai-jamjuree" data-font-name="Bai Jamjuree">Bai Jamjuree</option>
<option value="bakbak-one" data-font-name="Bakbak One">Bakbak One</option>
<option value="ballet" data-font-name="Ballet">Ballet</option>
<option value="baloo-2" data-font-name="Baloo 2">Baloo 2</option>
<option value="baloo-bhai-2" data-font-name="Baloo Bhai 2">Baloo Bhai 2</option>
<option value="baloo-bhaijaan-2" data-font-name="Baloo Bhaijaan 2">Baloo Bhaijaan 2</option>
<option value="baloo-bhaina-2" data-font-name="Baloo Bhaina 2">Baloo Bhaina 2</option>
<option value="baloo-chettan-2" data-font-name="Baloo Chettan 2">Baloo Chettan 2</option>
<option value="baloo-da-2" data-font-name="Baloo Da 2">Baloo Da 2</option>
<option value="baloo-paaji-2" data-font-name="Baloo Paaji 2">Baloo Paaji 2</option>
<option value="baloo-tamma-2" data-font-name="Baloo Tamma 2">Baloo Tamma 2</option>
<option value="baloo-tammudu-2" data-font-name="Baloo Tammudu 2">Baloo Tammudu 2</option>
<option value="baloo-thambi-2" data-font-name="Baloo Thambi 2">Baloo Thambi 2</option>
<option value="balsamiq-sans" data-font-name="Balsamiq Sans">Balsamiq Sans</option>
<option value="balthazar" data-font-name="Balthazar">Balthazar</option>
<option value="bangers" data-font-name="Bangers">Bangers</option>
<option value="barlow" data-font-name="Barlow">Barlow</option>
<option value="barlow-condensed" data-font-name="Barlow Condensed">Barlow Condensed</option>
<option value="barlow-semi-condensed" data-font-name="Barlow Semi Condensed">Barlow Semi Condensed</option>
<option value="barriecito" data-font-name="Barriecito">Barriecito</option>
<option value="barrio" data-font-name="Barrio">Barrio</option>
<option value="basic" data-font-name="Basic">Basic</option>
<option value="baskervville" data-font-name="Baskervville">Baskervville</option>
<option value="baskervville-sc" data-font-name="Baskervville SC">Baskervville SC</option>
<option value="battambang" data-font-name="Battambang">Battambang</option>
<option value="baumans" data-font-name="Baumans">Baumans</option>
<option value="bayon" data-font-name="Bayon">Bayon</option>
<option value="bbh-sans-bartle" data-font-name="BBH Sans Bartle">BBH Sans Bartle</option>
<option value="bbh-sans-bogle" data-font-name="BBH Sans Bogle">BBH Sans Bogle</option>
<option value="bbh-sans-hegarty" data-font-name="BBH Sans Hegarty">BBH Sans Hegarty</option>
<option value="be-vietnam-pro" data-font-name="Be Vietnam Pro">Be Vietnam Pro</option>
<option value="beau-rivage" data-font-name="Beau Rivage">Beau Rivage</option>
<option value="bebas-neue" data-font-name="Bebas Neue">Bebas Neue</option>
<option value="beiruti" data-font-name="Beiruti">Beiruti</option>
<option value="belanosima" data-font-name="Belanosima">Belanosima</option>
<option value="belgrano" data-font-name="Belgrano">Belgrano</option>
<option value="bellefair" data-font-name="Bellefair">Bellefair</option>
<option value="belleza" data-font-name="Belleza">Belleza</option>
<option value="bellota" data-font-name="Bellota">Bellota</option>
<option value="bellota-text" data-font-name="Bellota Text">Bellota Text</option>
<option value="benchnine" data-font-name="BenchNine">BenchNine</option>
<option value="benne" data-font-name="Benne">Benne</option>
<option value="bentham" data-font-name="Bentham">Bentham</option>
<option value="berkshire-swash" data-font-name="Berkshire Swash">Berkshire Swash</option>
<option value="besley" data-font-name="Besley">Besley</option>
<option value="beth-ellen" data-font-name="Beth Ellen">Beth Ellen</option>
<option value="bevan" data-font-name="Bevan">Bevan</option>
<option value="bhutuka-expanded-one" data-font-name="BhuTuka Expanded One">BhuTuka Expanded One</option>
<option value="big-shoulders" data-font-name="Big Shoulders">Big Shoulders</option>
<option value="big-shoulders-display" data-font-name="Big Shoulders Display">Big Shoulders Display</option>
<option value="big-shoulders-inline" data-font-name="Big Shoulders Inline">Big Shoulders Inline</option>
<option value="big-shoulders-inline-display" data-font-name="Big Shoulders Inline Display">Big Shoulders Inline Display</option>
<option value="big-shoulders-inline-text" data-font-name="Big Shoulders Inline Text">Big Shoulders Inline Text</option>
<option value="big-shoulders-stencil" data-font-name="Big Shoulders Stencil">Big Shoulders Stencil</option>
<option value="big-shoulders-stencil-display" data-font-name="Big Shoulders Stencil Display">Big Shoulders Stencil Display</option>
<option value="big-shoulders-stencil-text" data-font-name="Big Shoulders Stencil Text">Big Shoulders Stencil Text</option>
<option value="big-shoulders-text" data-font-name="Big Shoulders Text">Big Shoulders Text</option>
<option value="bigelow-rules" data-font-name="Bigelow Rules">Bigelow Rules</option>
<option value="bigshot-one" data-font-name="Bigshot One">Bigshot One</option>
<option value="bilbo" data-font-name="Bilbo">Bilbo</option>
<option value="bilbo-swash-caps" data-font-name="Bilbo Swash Caps">Bilbo Swash Caps</option>
<option value="biorhyme" data-font-name="BioRhyme">BioRhyme</option>
<option value="biorhyme-expanded" data-font-name="BioRhyme Expanded">BioRhyme Expanded</option>
<option value="birthstone" data-font-name="Birthstone">Birthstone</option>
<option value="birthstone-bounce" data-font-name="Birthstone Bounce">Birthstone Bounce</option>
<option value="biryani" data-font-name="Biryani">Biryani</option>
<option value="bitcount" data-font-name="Bitcount">Bitcount</option>
<option value="bitcount-grid-double" data-font-name="Bitcount Grid Double">Bitcount Grid Double</option>
<option value="bitcount-grid-double-ink" data-font-name="Bitcount Grid Double Ink">Bitcount Grid Double Ink</option>
<option value="bitcount-grid-single" data-font-name="Bitcount Grid Single">Bitcount Grid Single</option>
<option value="bitcount-grid-single-ink" data-font-name="Bitcount Grid Single Ink">Bitcount Grid Single Ink</option>
<option value="bitcount-ink" data-font-name="Bitcount Ink">Bitcount Ink</option>
<option value="bitcount-prop-double" data-font-name="Bitcount Prop Double">Bitcount Prop Double</option>
<option value="bitcount-prop-double-ink" data-font-name="Bitcount Prop Double Ink">Bitcount Prop Double Ink</option>
<option value="bitcount-prop-single" data-font-name="Bitcount Prop Single">Bitcount Prop Single</option>
<option value="bitcount-prop-single-ink" data-font-name="Bitcount Prop Single Ink">Bitcount Prop Single Ink</option>
<option value="bitcount-single" data-font-name="Bitcount Single">Bitcount Single</option>
<option value="bitcount-single-ink" data-font-name="Bitcount Single Ink">Bitcount Single Ink</option>
<option value="bitter" data-font-name="Bitter">Bitter</option>
<option value="biz-udgothic" data-font-name="BIZ UDGothic">BIZ UDGothic</option>
<option value="biz-udmincho" data-font-name="BIZ UDMincho">BIZ UDMincho</option>
<option value="biz-udpgothic" data-font-name="BIZ UDPGothic">BIZ UDPGothic</option>
<option value="biz-udpmincho" data-font-name="BIZ UDPMincho">BIZ UDPMincho</option>
<option value="black-and-white-picture" data-font-name="Black And White Picture">Black And White Picture</option>
<option value="black-han-sans" data-font-name="Black Han Sans">Black Han Sans</option>
<option value="black-ops-one" data-font-name="Black Ops One">Black Ops One</option>
<option value="blaka" data-font-name="Blaka">Blaka</option>
<option value="blaka-hollow" data-font-name="Blaka Hollow">Blaka Hollow</option>
<option value="blaka-ink" data-font-name="Blaka Ink">Blaka Ink</option>
<option value="blinker" data-font-name="Blinker">Blinker</option>
<option value="bodoni-moda" data-font-name="Bodoni Moda">Bodoni Moda</option>
<option value="bodoni-moda-sc" data-font-name="Bodoni Moda SC">Bodoni Moda SC</option>
<option value="bokor" data-font-name="Bokor">Bokor</option>
<option value="boldonse" data-font-name="Boldonse">Boldonse</option>
<option value="bona-nova" data-font-name="Bona Nova">Bona Nova</option>
<option value="bona-nova-sc" data-font-name="Bona Nova SC">Bona Nova SC</option>
<option value="bonbon" data-font-name="Bonbon">Bonbon</option>
<option value="bonheur-royale" data-font-name="Bonheur Royale">Bonheur Royale</option>
<option value="boogaloo" data-font-name="Boogaloo">Boogaloo</option>
<option value="borel" data-font-name="Borel">Borel</option>
<option value="bowlby-one" data-font-name="Bowlby One">Bowlby One</option>
<option value="bowlby-one-sc" data-font-name="Bowlby One SC">Bowlby One SC</option>
<option value="braah-one" data-font-name="Braah One">Braah One</option>
<option value="brawler" data-font-name="Brawler">Brawler</option>
<option value="bree-serif" data-font-name="Bree Serif">Bree Serif</option>
<option value="bricolage-grotesque" data-font-name="Bricolage Grotesque">Bricolage Grotesque</option>
<option value="briem-hand" data-font-name="Briem Hand">Briem Hand</option>
<option value="bruno-ace" data-font-name="Bruno Ace">Bruno Ace</option>
<option value="bruno-ace-sc" data-font-name="Bruno Ace SC">Bruno Ace SC</option>
<option value="brygada-1918" data-font-name="Brygada 1918">Brygada 1918</option>
<option value="bubblegum-sans" data-font-name="Bubblegum Sans">Bubblegum Sans</option>
<option value="bubbler-one" data-font-name="Bubbler One">Bubbler One</option>
<option value="buda" data-font-name="Buda">Buda</option>
<option value="buenard" data-font-name="Buenard">Buenard</option>
<option value="bungee" data-font-name="Bungee">Bungee</option>
<option value="bungee-hairline" data-font-name="Bungee Hairline">Bungee Hairline</option>
<option value="bungee-inline" data-font-name="Bungee Inline">Bungee Inline</option>
<option value="bungee-outline" data-font-name="Bungee Outline">Bungee Outline</option>
<option value="bungee-shade" data-font-name="Bungee Shade">Bungee Shade</option>
<option value="bungee-spice" data-font-name="Bungee Spice">Bungee Spice</option>
<option value="bungee-tint" data-font-name="Bungee Tint">Bungee Tint</option>
<option value="butcherman" data-font-name="Butcherman">Butcherman</option>
<option value="butterfly-kids" data-font-name="Butterfly Kids">Butterfly Kids</option>
<option value="bytesized" data-font-name="Bytesized">Bytesized</option>
<option value="cabin" data-font-name="Cabin">Cabin</option>
<option value="cabin-condensed" data-font-name="Cabin Condensed">Cabin Condensed</option>
<option value="cabin-sketch" data-font-name="Cabin Sketch">Cabin Sketch</option>
<option value="cactus-classical-serif" data-font-name="Cactus Classical Serif">Cactus Classical Serif</option>
<option value="caesar-dressing" data-font-name="Caesar Dressing">Caesar Dressing</option>
<option value="cagliostro" data-font-name="Cagliostro">Cagliostro</option>
<option value="cairo" data-font-name="Cairo">Cairo</option>
<option value="cairo-play" data-font-name="Cairo Play">Cairo Play</option>
<option value="cal-sans" data-font-name="Cal Sans">Cal Sans</option>
<option value="caladea" data-font-name="Caladea">Caladea</option>
<option value="calistoga" data-font-name="Calistoga">Calistoga</option>
<option value="calligraffitti" data-font-name="Calligraffitti">Calligraffitti</option>
<option value="cambay" data-font-name="Cambay">Cambay</option>
<option value="cambo" data-font-name="Cambo">Cambo</option>
<option value="candal" data-font-name="Candal">Candal</option>
<option value="cantarell" data-font-name="Cantarell">Cantarell</option>
<option value="cantata-one" data-font-name="Cantata One">Cantata One</option>
<option value="cantora-one" data-font-name="Cantora One">Cantora One</option>
<option value="caprasimo" data-font-name="Caprasimo">Caprasimo</option>
<option value="capriola" data-font-name="Capriola">Capriola</option>
<option value="caramel" data-font-name="Caramel">Caramel</option>
<option value="carattere" data-font-name="Carattere">Carattere</option>
<option value="cardo" data-font-name="Cardo">Cardo</option>
<option value="carlito" data-font-name="Carlito">Carlito</option>
<option value="carme" data-font-name="Carme">Carme</option>
<option value="carrois-gothic" data-font-name="Carrois Gothic">Carrois Gothic</option>
<option value="carrois-gothic-sc" data-font-name="Carrois Gothic SC">Carrois Gothic SC</option>
<option value="carter-one" data-font-name="Carter One">Carter One</option>
<option value="cascadia-code" data-font-name="Cascadia Code">Cascadia Code</option>
<option value="cascadia-mono" data-font-name="Cascadia Mono">Cascadia Mono</option>
<option value="castoro" data-font-name="Castoro">Castoro</option>
<option value="castoro-titling" data-font-name="Castoro Titling">Castoro Titling</option>
<option value="catamaran" data-font-name="Catamaran">Catamaran</option>
<option value="caudex" data-font-name="Caudex">Caudex</option>
<option value="caveat" data-font-name="Caveat">Caveat</option>
<option value="caveat-brush" data-font-name="Caveat Brush">Caveat Brush</option>
<option value="cedarville-cursive" data-font-name="Cedarville Cursive">Cedarville Cursive</option>
<option value="ceviche-one" data-font-name="Ceviche One">Ceviche One</option>
<option value="chakra-petch" data-font-name="Chakra Petch">Chakra Petch</option>
<option value="changa" data-font-name="Changa">Changa</option>
<option value="changa-one" data-font-name="Changa One">Changa One</option>
<option value="chango" data-font-name="Chango">Chango</option>
<option value="charis-sil" data-font-name="Charis SIL">Charis SIL</option>
<option value="charm" data-font-name="Charm">Charm</option>
<option value="charmonman" data-font-name="Charmonman">Charmonman</option>
<option value="chathura" data-font-name="Chathura">Chathura</option>
<option value="chau-philomene-one" data-font-name="Chau Philomene One">Chau Philomene One</option>
<option value="chela-one" data-font-name="Chela One">Chela One</option>
<option value="chelsea-market" data-font-name="Chelsea Market">Chelsea Market</option>
<option value="chenla" data-font-name="Chenla">Chenla</option>
<option value="cherish" data-font-name="Cherish">Cherish</option>
<option value="cherry-bomb-one" data-font-name="Cherry Bomb One">Cherry Bomb One</option>
<option value="cherry-cream-soda" data-font-name="Cherry Cream Soda">Cherry Cream Soda</option>
<option value="cherry-swash" data-font-name="Cherry Swash">Cherry Swash</option>
<option value="chewy" data-font-name="Chewy">Chewy</option>
<option value="chicle" data-font-name="Chicle">Chicle</option>
<option value="chilanka" data-font-name="Chilanka">Chilanka</option>
<option value="chiron-goround-tc" data-font-name="Chiron GoRound TC">Chiron GoRound TC</option>
<option value="chiron-hei-hk" data-font-name="Chiron Hei HK">Chiron Hei HK</option>
<option value="chiron-sung-hk" data-font-name="Chiron Sung HK">Chiron Sung HK</option>
<option value="chivo" data-font-name="Chivo">Chivo</option>
<option value="chivo-mono" data-font-name="Chivo Mono">Chivo Mono</option>
<option value="chocolate-classical-sans" data-font-name="Chocolate Classical Sans">Chocolate Classical Sans</option>
<option value="chokokutai" data-font-name="Chokokutai">Chokokutai</option>
<option value="chonburi" data-font-name="Chonburi">Chonburi</option>
<option value="cinzel" data-font-name="Cinzel">Cinzel</option>
<option value="cinzel-decorative" data-font-name="Cinzel Decorative">Cinzel Decorative</option>
<option value="clicker-script" data-font-name="Clicker Script">Clicker Script</option>
<option value="climate-crisis" data-font-name="Climate Crisis">Climate Crisis</option>
<option value="coda" data-font-name="Coda">Coda</option>
<option value="coda-caption" data-font-name="Coda Caption">Coda Caption</option>
<option value="codystar" data-font-name="Codystar">Codystar</option>
<option value="coiny" data-font-name="Coiny">Coiny</option>
<option value="combo" data-font-name="Combo">Combo</option>
<option value="comfortaa" data-font-name="Comfortaa">Comfortaa</option>
<option value="comforter" data-font-name="Comforter">Comforter</option>
<option value="comforter-brush" data-font-name="Comforter Brush">Comforter Brush</option>
<option value="comic-neue" data-font-name="Comic Neue">Comic Neue</option>
<option value="comic-relief" data-font-name="Comic Relief">Comic Relief</option>
<option value="coming-soon" data-font-name="Coming Soon">Coming Soon</option>
<option value="comme" data-font-name="Comme">Comme</option>
<option value="commissioner" data-font-name="Commissioner">Commissioner</option>
<option value="concert-one" data-font-name="Concert One">Concert One</option>
<option value="condiment" data-font-name="Condiment">Condiment</option>
<option value="content" data-font-name="Content">Content</option>
<option value="contrail-one" data-font-name="Contrail One">Contrail One</option>
<option value="convergence" data-font-name="Convergence">Convergence</option>
<option value="cookie" data-font-name="Cookie">Cookie</option>
<option value="copse" data-font-name="Copse">Copse</option>
<option value="coral-pixels" data-font-name="Coral Pixels">Coral Pixels</option>
<option value="corben" data-font-name="Corben">Corben</option>
<option value="corinthia" data-font-name="Corinthia">Corinthia</option>
<option value="cormorant" data-font-name="Cormorant">Cormorant</option>
<option value="cormorant-garamond" data-font-name="Cormorant Garamond">Cormorant Garamond</option>
<option value="cormorant-infant" data-font-name="Cormorant Infant">Cormorant Infant</option>
<option value="cormorant-sc" data-font-name="Cormorant SC">Cormorant SC</option>
<option value="cormorant-unicase" data-font-name="Cormorant Unicase">Cormorant Unicase</option>
<option value="cormorant-upright" data-font-name="Cormorant Upright">Cormorant Upright</option>
<option value="cossette-texte" data-font-name="Cossette Texte">Cossette Texte</option>
<option value="cossette-titre" data-font-name="Cossette Titre">Cossette Titre</option>
<option value="courgette" data-font-name="Courgette">Courgette</option>
<option value="courier-prime" data-font-name="Courier Prime">Courier Prime</option>
<option value="cousine" data-font-name="Cousine">Cousine</option>
                        </select>
                    </div>

                </div>
            </div>

            <!-- [01C] Welcome Screen --><div class="brutalist-card p-5">
                <h2 class="text-xl font-bold brutalist-title border-b border-black pb-3 mb-4">
                    [01C] EKRAN POWITALNY
                </h2>
                <div class="grid grid-cols-1 gap-4">
                    <div class="col-span-2">
                        <label for="welcome-title" class="block text-xs font-bold mb-1">TEKST TYTUŁU POWITALNEGO</label>
                        <input type="text" id="welcome-title" class="w-full brutalist-input" value="Pomóż nam zadbać o Twoje potrzeby">
                    </div>
                    <!-- NOWY SUWAK -->
                    <div class="col-span-2">
                        <label for="welcome-title-size" class="block text-xs font-bold mb-1">
                            ROZMIAR FONTU TYTUŁU: <span id="welcome-title-size-value" class="font-mono font-bold text-[var(--color-primary)]">24px</span>
                        </label>
                        <input type="range" id="welcome-title-size" class="w-full" value="24" min="14" max="48">
                    </div>
                    
                    <div class="col-span-2">
                        <label for="welcome-button-text" class="block text-xs font-bold mb-1">TEKST PRZYCISKU</label>
                        <input type="text" id="welcome-button-text" class="w-full brutalist-input" value="Wypełnij ankietę">
                    </div>
                    <div>
                        <label for="welcome-button-font-size" class="block text-xs font-bold mb-1">
                            ROZMIAR FONTU PRZYCISKU: <span id="welcome-button-font-size-value" class="font-mono font-bold text-[var(--color-primary)]">16px</span>
                        </label>
                        <input type="range" id="welcome-button-font-size" class="w-full" value="16" min="10" max="32">
                    </div>
                    <div>
                        <label for="welcome-button-padding" class="block text-xs font-bold mb-1">
                            PADDING PRZYCISKU: <span id="welcome-button-padding-value" class="font-mono font-bold text-[var(--color-primary)]">10px 20px</span>
                        </label>
                        <input type="range" id="welcome-button-padding" class="w-full" value="10" min="5" max="25">
                    </div>

                    <!-- NOWE KONTROLKI KOLORÓW PRZYCISKU -->
                    <div class="config-item">
                        <label for="welcome-button-bg-color-text" class="block text-xs font-bold mb-1">TŁO PRZYCISKU</label>
                        <div class="flex gap-2">
                            <input type="color" id="welcome-button-bg-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#000000">
                            <input type="text" id="welcome-button-bg-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#000000" placeholder="#000000">
                        </div>
                    </div>
                    <div class="config-item">
                        <label for="welcome-button-text-color-text" class="block text-xs font-bold mb-1">TEKST PRZYCISKU</label>
                        <div class="flex gap-2">
                            <input type="color" id="welcome-button-text-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#ffffff">
                            <input type="text" id="welcome-button-text-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#ffffff" placeholder="#FFFFFF">
                        </div>
                    </div>
                    <div class="config-item col-span-2">
                        <label for="welcome-button-hover-bg-color-text" class="block text-xs font-bold mb-1">TŁO HOVER PRZYCISKU</label>
                        <div class="flex gap-2">
                            <input type="color" id="welcome-button-hover-bg-color" class="brutalist-input p-0 h-10" style="min-width: 48px; width: 48px;" value="#333333">
                            <input type="text" id="welcome-button-hover-bg-color-text" class="flex-1 brutalist-input font-mono uppercase" style="min-width: 70px;" value="#333333" placeholder="#333333">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. Zaawansowane Ustawienia Stylów --><div class="brutalist-card p-5">
                <h2 class="text-xl font-bold brutalist-title border-b border-black pb-3 mb-4">
                    [02] MANUALNY ROZMIAR I ODSTĘPY
                </h2>
                
                <div class="mb-4">
                    <label class="flex items-center gap-2 cursor-pointer">
                        <input type="checkbox" id="manual-sizing">
                        <span class="text-xs font-bold">WŁĄCZ MANUALNE NADPISANIE ROZMIARÓW (PX)</span>
                    </label>
                </div>
                <div class="mb-4">
                    <label class="flex items-center gap-2 cursor-pointer">
                        <input type="checkbox" id="question-numbering" checked>
                        <span class="text-xs font-bold">WŁĄCZ NUMERACJĘ PYTAŃ (NP. 1/6)</span>
                    </label>
                </div>


                <!-- Manualne kontrolki --><div id="manual-controls" class="grid grid-cols-2 gap-4" style="display: none;">
                    
                    <div>
                        <label for="header-padding" class="block text-xs font-bold mb-1">
                            PADDING NAGŁÓWKA: <span id="header-padding-value" class="font-mono font-bold text-[var(--color-primary)]">8px</span>
                        </label>
                        <input type="range" id="header-padding" class="w-full" value="8" min="4" max="20">
                    </div>
                    
                    <div>
                        <label for="spacing-size" class="block text-xs font-bold mb-1">
                            ODSTĘPY (PIONOWY GAP): <span id="spacing-size-value" class="font-mono font-bold text-[var(--color-primary)]">12px</span>
                        </label>
                        <input type="range" id="spacing-size" class="w-full" value="12" min="0" max="30">
                    </div>

                    <div>
                        <label for="header-text-size" class="block text-xs font-bold mb-1">
                            NAGŁÓWEK TOP: <span id="header-text-size-value" class="font-mono font-bold text-[var(--color-primary)]">12px</span>
                        </label>
                        <input type="range" id="header-text-size" class="w-full" value="12" min="8" max="24">
                    </div>
                    <div>
                        <label for="question-font-size" class="block text-xs font-bold mb-1">
                            TEKST PYTANIA: <span id="question-font-size-value" class="font-mono font-bold text-[var(--color-primary)]">18px</span>
                        </label>
                        <input type="range" id="question-font-size" class="w-full" value="18" min="10" max="40">
                    </div>
                    <div>
                        <label for="answer-font-size" class="block text-xs font-bold mb-1">
                            TEKST ODPOWIEDZI: <span id="answer-font-size-value" class="font-mono font-bold text-[var(--color-primary)]">14px</span>
                        </label>
                        <input type="range" id="answer-font-size" class="w-full" value="14" min="8" max="30">
                    </div>
                    <div>
                        <label for="radio-size" class="block text-xs font-bold mb-1">
                            ROZMIAR RADIO: <span id="radio-size-value" class="font-mono font-bold text-[var(--color-primary)]">18px</span>
                        </label>
                        <input type="range" id="radio-size" class="w-full" value="18" min="12" max="30">
                    </div>
                    
                    <div>
                        <label for="thankyou-title-size" class="block text-xs font-bold mb-1">
                            TYTUŁ KOŃCOWY: <span id="thankyou-title-size-value" class="font-mono font-bold text-[var(--color-primary)]">24px</span>
                        </label>
                        <input type="range" id="thankyou-title-size" class="w-full" value="24" min="14" max="48">
                    </div>

                    <div>
                        <label for="thankyou-message-size" class="block text-xs font-bold mb-1">
                            WIAD. KOŃCOWA: <span id="thankyou-message-size-value" class="font-mono font-bold text-[var(--color-primary)]">16px</span>
                        </label>
                        <input type="range" id="thankyou-message-size" class="w-full" value="16" min="10" max="32">
                    </div>

                    <div class="col-span-2">
                        <label for="padding-size" class="block text-xs font-bold mb-1">
                            GLOBALNY PADDING (WNĘTRZE): <span id="padding-size-value" class="font-mono font-bold text-[var(--color-primary)]">20px</span>
                        </label>
                        <input type="range" id="padding-size" class="w-full" value="20" min="10" max="50">
                    </div>

                    <!-- Globalny Górny Padding Treści -->
                    <div class="col-span-2">
                        <label for="content-top-padding" class="block text-xs font-bold mb-1">
                            GÓRNY PADDING TREŚCI: <span id="content-top-padding-value" class="font-mono font-bold text-[var(--color-primary)]">20px</span>
                        </label>
                        <input type="range" id="content-top-padding" class="w-full" value="20" min="0" max="50">
                    </div>

                    <!-- Globalny odstęp Pytanie-Odpowiedzi -->
                    <div class="col-span-2">
                        <label for="global-question-answer-gap" class="block text-xs font-bold mb-1">
                            GLOBALNY ODSTĘP P-O (BAZA): <span id="global-question-answer-gap-value" class="font-mono font-bold text-[var(--color-primary)]">5px</span>
                        </label>
                        <input type="range" id="global-question-answer-gap" class="w-full" value="5" min="0" max="50">
                    </div>

                </div>
            </div>

            <!-- 3. NOWA KARTA: Ekran Końcowy --><div class="brutalist-card p-5">
                <h2 class="text-xl font-bold brutalist-title border-b border-black pb-3 mb-4">
                    [03] EKRAN KOŃCOWY
                </h2>
                
                <div class="grid grid-cols-1 gap-4">
                    <div class="col-span-2">
                        <label for="thank-you-title" class="block text-xs font-bold mb-1">TEKST TYTUŁU KOŃCOWEGO</label>
                        <input type="text" id="thank-you-title" class="w-full brutalist-input" value="DZIĘKUJEMY.">
                    </div>
                    <div class="col-span-2">
                        <label for="thank-you-message" class="block text-xs font-bold mb-1">TEKST WIADOMOŚCI KOŃCOWEJ</label>
                        <input type="text" id="thank-you-message" class="w-full brutalist-input" value="TWOJA ODPOWIEDŹ ZAPISANA.">
                    </div>
                </div>
            </div>

            <!-- 4. Import Pytań z Pliku --><div class="brutalist-card p-5">
                <h2 class="text-xl font-bold brutalist-title border-b border-black pb-3 mb-4">
                    [04] ZARZĄDZANIE PYTANIAMI
                </h2>
                
                <div class="mb-3">
                    <label class="block text-xs font-bold mb-2">
                        WKLEJ BLOK PYTAŃ LUB WYBIERZ PLIK .TXT
                    </label>
                    
                    <input type="file" id="questions-file-input" accept=".txt" class="hidden">
                    <button id="choose-file-btn" class="w-full brutalist-btn brutalist-btn-secondary text-sm mb-3">
                        WYBIERZ PLIK .TXT
                    </button>
                    
                    <div class="text-center text-xs font-mono text-black mb-2">--- LUB ---</div>
                    
                    <textarea id="questions-text-input" class="w-full brutalist-input font-mono" rows="8" placeholder="Wklej pytania tutaj:

1. Pytanie 1?
Odp A
Odp B

2. Pytanie 2?
Odp C
Odp D"></textarea>
                </div>
                
                <button id="import-questions-btn" class="w-full brutalist-btn brutalist-btn-accent text-sm">
                    IMPORTUJ & ZASTĄP
                </button>
                
                <div class="mt-3 text-[10px] text-black">
                    **FORMAT:** Numerowane pytanie ("1. Tekst?"), po nim odpowiedzi w osobnych liniach.
                </div>
            </div>

            <!-- 5. Kontener na Pytania --><div id="questions-container" class="flex flex-col gap-5">
                <!-- Pytania będą dodawane tutaj dynamicznie --></div>

            <!-- 6. Przycisk dodawania pytania --><button id="add-question-btn" class="w-full brutalist-btn brutalist-btn-primary flex-shrink-0">
                + DODAJ NOWE PYTANIE
            </button>
        </div>

        <!-- 
          PRAWA STRONA (WRAPPER):
          - 'w-full' (mobilny) - zajmuje całą szerokość.
          - 'flex-grow' (desktop) - zajmuje resztę miejsca.
          - 'p-6' tworzy odstęp (ramkę) dookoła.
        -->
        <div class="flex-grow p-6 w-full">
            <!-- 
              PANEL PODGLĄDU (WŁAŚCIWY):
              ZMIANA (PODWÓJNA RAMKA): Dodano 'p-5', aby odseparować dzieci
              (h3 i preview-container) od 4px ramki 'brutalist-panel-heavy'.
            -->
            <div class="brutalist-title brutalist-panel-heavy flex flex-col overflow-hidden h-full p-5">
                <!-- 
                  ZMIANA (PODWÓJNA RAMKA): Usunięto 'px-5 pt-5',
                  ponieważ rodzic ('brutalist-panel-heavy') ma teraz 'p-5'.
                -->
                <h3 class="text-xl font-bold text-black mb-4 brutalist-title border-b border-black pb-3 flex-shrink-0">
                    [05] PODGLĄD W CZASIE RZECZYWISTYM
                </h3>
                <!-- 
                  Ten element ma teraz odstęp od rodzica (dzięki p-5) 
                  oraz odstęp od h3 (dzięki mb-4).
                -->
                <div id="preview-container" class="brutalist-preview-area flex-grow overflow-y-auto p-5">
                    <!-- Dynamicznie generowane iframe'y dla każdego pytania --></div>
            </div>
        </div>

    </main>

    <!-- Szablon Pytania (niewidoczny) --><template id="question-template">
        <div class="brutalist-card p-5" data-question-id="">
            <div class="question-drag-handle flex justify-between items-center border-b border-black pb-3 mb-4">
                <div class="flex items-center gap-2">
                    <h3 class="text-lg font-bold brutalist-title">PYTANIE <span class="question-number text-[var(--color-accent)]">1</span></h3>
                    <div class="flex gap-1">
                        <button class="move-question-up-btn brutalist-move-btn" title="Przesuń w górę">▲</button>
                        <button class="move-question-down-btn brutalist-move-btn" title="Przesuń w dół">▼</button>
                    </div>
                </div>
                <button class="remove-question-btn text-xs font-bold hover:text-red-600 transition-none">
                    [X] USUŃ
                </button>
            </div>
            
            <div class="question-content-wrapper">
                <div>
                    <label class="block text-xs font-bold mb-1">TREŚĆ PYTANIA</label>
                    <textarea class="question-text w-full brutalist-input"></textarea>
                </div>

                <div class="answers-container mt-3 flex flex-col gap-2">
                    <!-- Pojedyncze odpowiedzi będą tutaj --></div>
                
                <button class="add-answer-btn text-xs font-bold text-black hover:text-[var(--color-accent)] mt-3 transition-none">
                    + DODAJ ODPOWIEDŹ
                </button>
                
                <div class="flex items-center gap-2 mt-4">
                    <input type="checkbox" class="randomize-answers">
                    <label class="text-xs font-bold">LOSOWA KOLEJNOŚĆ ODPOWIEDZI</label>
                </div>

                <div class="mt-4 select-wrapper">
                    <label class="block text-xs font-bold mb-1">UKŁAD ODPOWIEDZI</label>
                    <div>
                        <select class="answers-layout w-full brutalist-input brutalist-select">
                            <option value="1-col">1 Kolumna (Standard)</option>
                            <option value="2-col">2 Kolumny (Grid)</option>
                        </select>
                    </div>
                </div>

                <!-- NOWOŚĆ: Suwak proporcji dla 2 kolumn -->
                <div class="column-width-slider-container mt-4" style="display: none;">
                    <label class="block text-xs font-bold mb-1">
                        ROZKŁAD KOLUMN (L:P): <span class="column-width-value font-mono font-bold text-[var(--color-primary)]">50% / 50%</span>
                    </label>
                    <input type="range" class="column-width-slider w-full" value="50" min="20" max="80">
                </div>

                <!-- Indywidualne suwaki odstępów -->
                <div class="mt-4 brutalist-inner-card grid grid-cols-1 gap-3">
                    <!-- Indywidualny suwak Pytanie-Odpowiedzi -->
                    <div>
                        <label for="" class="block text-xs font-bold mb-1">
                            INDYWIDUALNY ODSTĘP P-O (DODATEK): <span class="question-answer-gap-value font-mono font-bold text-[var(--color-primary)]">0px</span>
                        </label>
                        <input type="range" class="question-answer-gap-slider w-full" value="0" min="0" max="50">
                    </div>
                    <div>
                        <label for="" class="block text-xs font-bold mb-1">
                            ODSTĘP MIĘDZY ODPOWIEDZIAMI: <span class="answer-spacing-value font-mono font-bold text-[var(--color-primary)]">0px</span>
                        </label>
                        <input type="range" class="answer-spacing-slider w-full" value="0" min="0" max="30">
                    </div>
                </div>
                <!-- Koniec indywidualnych suwaków -->


                <div class="mt-4 brutalist-inner-card">
                    <h4 class="text-xs font-bold brutalist-title mb-2">LOGIKA WARUNKOWA (OPTIONAL)</h4>
                    
                    <div class="flex items-center gap-2 mb-3">
                        <input type="checkbox" class="conditional-enabled">
                        <label class="text-xs font-bold">AKTYWUJ WARUNEK</label>
                    </div>

                    <div class="conditional-settings grid grid-cols-1 gap-2" style="display: none;">
                        <div class="select-wrapper">
                            <label class="block text-[10px] font-bold mb-1">POKAŻ TYLKO GDY W PYTANIU #:</label>
                            <select class="conditional-question-ref w-full brutalist-input brutalist-select">
                                <option value="">-- WYBIERZ PYTANIE PRZEDNIE --</option>
                            </select>
                        </div>
                        <div class="select-wrapper">
                            <label class="block text-[10px] font-bold mb-1">WYBRANO ODPOWIEDŹ:</label>
                            <select class="conditional-answer-ref w-full brutalist-input brutalist-select">
                                <option value="">-- NAJPIERW WYBIERZ PYTANIE --</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div> <!-- Koniec question-content-wrapper -->
        </div>
    </template>

    <!-- Szablon Odpowiedzi (niewidoczny) --><template id="answer-template">
        <div class="answer-input flex items-start gap-2">
            <textarea class="answer-text brutalist-input flex-1" placeholder="TREŚĆ ODPOWIEDZI" rows="1"></textarea>
            <button class="remove-answer-btn flex-shrink-0 text-xs font-bold text-red-600 hover:text-black transition-none">
                [X]
            </button>
        </div>
    </template>
    
    <!-- 
      ZMIANA: Usunięto cały szablon <template id="banner-template">
      Został on przeniesiony do pliku preview.js jako stała stringowa,
      aby zapobiec "naprawianiu" kodu HTML przez przeglądarkę.
    -->
    
    <script type="module" src="main.js"></script>

    <!-- NOWOŚĆ: Ekran ładowania dla generatora -->
    <div id="loading-overlay" class="brutalist-loading-overlay">
        <div class="brutalist-spinner"></div>
        <div class="brutalist-loading-text">
            POBIERANIE I OSADZANIE FONTÓW...
        </div>
    </div>
    <!-- KONIEC EKRANU ŁADOWANIA -->

</body>
</html>