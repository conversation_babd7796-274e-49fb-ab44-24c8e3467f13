// config.js - Definicje kontrolek i pól

// Obiekt przechowujący referencje do elementów DOM
export const controls = {};

// Lista pól, których zmiana wyzwala odświeżenie podglądu
export const previewUpdatingFields = [];

/**
 * Wypełnia obiekt 'controls' i 'previewUpdatingFields'
 */
export function initControls() {
    Object.assign(controls, {
        endpointUrl: document.getElementById('endpoint-url'),
        exitUrl: document.getElementById('exit-url'),
        bannerSize: document.getElementById('banner-size'),
        // ZMIANA: Zastąpiono `fontPackageName` i `fontFamily` nową listą
        fontSelection: document.getElementById('font-selection'),
        backgroundColor: document.getElementById('background-color'),
        backgroundColorText: document.getElementById('background-color-text'),
        answerHoverColor: document.getElementById('answer-hover-color'),
        answerHoverColorText: document.getElementById('answer-hover-color-text'),
        questionsContainer: document.getElementById('questions-container'),
        previewContainer: document.getElementById('preview-container'),
        addQuestionBtn: document.getElementById('add-question-btn'),
        generateBtn: document.getElementById('generate-banner-btn'),
        loadBannerBtn: document.getElementById('load-banner-btn'),
        loadBannerInput: document.getElementById('load-banner-input'),
        // Kontrolki manualnych rozmiarów
        manualSizing: document.getElementById('manual-sizing'),
        headerTextSize: document.getElementById('header-text-size'),
        headerPadding: document.getElementById('header-padding'), 
        questionFontSize: document.getElementById('question-font-size'),
        answerFontSize: document.getElementById('answer-font-size'),
        radioSize: document.getElementById('radio-size'),
        spacingSize: document.getElementById('spacing-size'), 
        paddingSize: document.getElementById('padding-size'),
        // NOWOŚĆ: Globalny górny padding
        contentTopPadding: document.getElementById('content-top-padding'),
        // NOWOŚĆ: Globalny odstęp P-O
        globalQuestionAnswerGap: document.getElementById('global-question-answer-gap'),
        thankYouTitleSize: document.getElementById('thankyou-title-size'), 
        thankYouMessageSize: document.getElementById('thankyou-message-size'), 
        thankYouTitle: document.getElementById('thank-you-title'),
        thankYouMessage: document.getElementById('thank-you-message'),
        // Welcome Screen
        welcomeTitle: document.getElementById('welcome-title'),
        welcomeTitleSize: document.getElementById('welcome-title-size'), // NOWY SUWAK
        welcomeButtonText: document.getElementById('welcome-button-text'),
        welcomeButtonFontSize: document.getElementById('welcome-button-font-size'),
        welcomeButtonPadding: document.getElementById('welcome-button-padding'),
        welcomeButtonBgColor: document.getElementById('welcome-button-bg-color'), // NOWY KOLOR
        welcomeButtonBgColorText: document.getElementById('welcome-button-bg-color-text'), // NOWY KOLOR
        welcomeButtonTextColor: document.getElementById('welcome-button-text-color'), // NOWY KOLOR
        welcomeButtonTextColorText: document.getElementById('welcome-button-text-color-text'), // NOWY KOLOR
        welcomeButtonHoverBgColor: document.getElementById('welcome-button-hover-bg-color'), // NOWY KOLOR
        welcomeButtonHoverBgColorText: document.getElementById('welcome-button-hover-bg-color-text'), // NOWY KOLOR
        // Question Numbering
        questionNumbering: document.getElementById('question-numbering'),
        // Przyciski importu
        chooseFileBtn: document.getElementById('choose-file-btn'),
        questionsFileInput: document.getElementById('questions-file-input'),
        questionsTextInput: document.getElementById('questions-text-input'),
        importQuestionsBtn: document.getElementById('import-questions-btn'),

        // Kontrolki stylów banera
        headerBgColor: document.getElementById('header-bg-color'),
        headerBgColorText: document.getElementById('header-bg-color-text'),
        headerFontColor: document.getElementById('header-font-color'),
        headerFontColorText: document.getElementById('header-font-color-text'),
        questionFontColor: document.getElementById('question-font-color'),
        questionFontColorText: document.getElementById('question-font-color-text'),
        answerFontColor: document.getElementById('answer-font-color'),
        answerFontColorText: document.getElementById('answer-font-color-text'),
        radioBgColor: document.getElementById('radio-bg-color'),
        radioBgColorText: document.getElementById('radio-bg-color-text'),
        radioBorderColor: document.getElementById('radio-border-color'),
        radioBorderColorText: document.getElementById('radio-border-color-text'),
        radioDotColor: document.getElementById('radio-dot-color'),
        radioDotColorText: document.getElementById('radio-dot-color-text')
    });

    // Wypełnij listę pól, które wyzwalają aktualizację podglądu
    previewUpdatingFields.push(
        controls.endpointUrl, 
        controls.exitUrl, 
        controls.bannerSize, 
        // ZMIANA: Zaktualizowano pole fontu
        controls.fontSelection,
        controls.backgroundColor,
        controls.backgroundColorText,
        controls.answerHoverColor,
        controls.answerHoverColorText,
        controls.headerTextSize,
        controls.headerPadding, 
        controls.questionFontSize,
        controls.answerFontSize,
        controls.radioSize,
        controls.spacingSize,
        controls.paddingSize,
        // NOWOŚĆ: Globalny górny padding
        controls.contentTopPadding,
        // NOWOŚĆ: Globalny odstęp P-O
        controls.globalQuestionAnswerGap,
        controls.thankYouTitleSize,
        controls.thankYouMessageSize,
        controls.thankYouTitle,
        controls.thankYouMessage,
        controls.manualSizing,
        controls.welcomeTitle,
        controls.welcomeTitleSize, // DODANE
        controls.welcomeButtonText,
        controls.welcomeButtonFontSize,
        controls.welcomeButtonPadding,
        controls.welcomeButtonBgColor, // DODANE
        controls.welcomeButtonBgColorText, // DODANE
        controls.welcomeButtonTextColor, // DODANE
        controls.welcomeButtonTextColorText, // DODANE
        controls.welcomeButtonHoverBgColor, // DODANE
        controls.welcomeButtonHoverBgColorText, // DODANE
        controls.questionNumbering,

        // Kontrolki stylów
        controls.headerBgColor,
        controls.headerBgColorText,
        controls.headerFontColor,
        controls.headerFontColorText,
        controls.questionFontColor,
        controls.questionFontColorText,
        controls.answerFontColor,
        controls.answerFontColorText,
        controls.radioBgColor,
        controls.radioBgColorText,
        controls.radioBorderColor,
        controls.radioBorderColorText,
        controls.radioDotColor,
        controls.radioDotColorText
    );
}