// ui.js - Wszystkie funkcje do manipulacji DOM edytora

import { controls } from './config.js';
import { updatePreview } from './preview.js';

let questionCounter = 0;

/**
 * <PERSON><PERSON>ja do autoresize textarea
 */
export function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = (textarea.scrollHeight) + 'px'; 
}

/**
 * Dodaje nową odpowiedź do konkretnej karty pytania
 */
function addAnswerToQuestion(questionCard, answerText = '') {
    const answersContainer = questionCard.querySelector('.answers-container');
    const template = document.getElementById('answer-template');
    const clone = template.content.cloneNode(true);
    
    const answerField = clone.querySelector('.answer-text');

    if (answerText) {
        answerField.value = answerText;
    }
    
    answersContainer.appendChild(clone);
    autoResizeTextarea(answerField);
}

/**
 * Wypełnia dropdown odpowiedzi na podstawie wybranego pytania
 */
export function populateConditionalAnswers(questionRefSelect) {
    const questionCard = questionRefSelect.closest('[data-question-id]');
    const answerSelect = questionCard.querySelector('.conditional-answer-ref');
    const selectedQuestionId = questionRefSelect.value;
    
    answerSelect.innerHTML = ''; 

    if (!selectedQuestionId) {
        answerSelect.innerHTML = '<option value="">-- NAJPIERW WYBIERZ PYTANIE --</option>';
        return;
    }

    const sourceQuestionCard = controls.questionsContainer.querySelector(`[data-question-id="${selectedQuestionId}"]`);
    
    if (!sourceQuestionCard) {
        answerSelect.innerHTML = '<option value="">BŁĄD: Nie znaleziono pytania</option>';
        return;
    }

    const answerInputs = sourceQuestionCard.querySelectorAll('.answer-text');
    
    answerSelect.innerHTML = '<option value="">-- WYBIERZ ODPOWIEDŹ --</option>';
    
    answerInputs.forEach(input => {
        const answerText = input.value.trim();
        if (answerText) {
            const answerValue = answerText.replace(/\n/g, '<br>');
            answerSelect.add(new Option(answerText, answerValue));
        }
    });
}


/**
 * Aktualizuje listę dostępnych pytań w dropdownie warunku
 */
function updateConditionalQuestionList(currentQuestionCard) {
    const currentQuestionId = parseInt(currentQuestionCard.getAttribute('data-question-id'));
    const select = currentQuestionCard.querySelector('.conditional-question-ref');
    const allQuestions = Array.from(controls.questionsContainer.querySelectorAll('[data-question-id]'));
    
    const oldValue = select.value;
    
    select.innerHTML = '<option value="">-- WYBIERZ PYTANIE PRZEDNIE --</option>';
    
    allQuestions.forEach(q => {
        const qId = parseInt(q.getAttribute('data-question-id'));
        if (qId < currentQuestionId) {
            const questionText = q.querySelector('.question-text').value.trim();
            const shortText = questionText.length > 40 ? questionText.substring(0, 40) + '...' : questionText;
            const option = document.createElement('option');
            option.value = qId;
            option.textContent = `Pytanie ${qId}: ${shortText}`;
            select.appendChild(option);
        }
    });
    
    if (oldValue && parseInt(oldValue) < currentQuestionId) {
        select.value = oldValue;
    }
}

/**
 * Aktualizuje listy warunkowe we wszystkich pytaniach
 */
export function updateAllConditionalLists() {
    const allQuestions = controls.questionsContainer.querySelectorAll('[data-question-id]');
    allQuestions.forEach(q => {
        const conditionalEnabled = q.querySelector('.conditional-enabled');
        if (conditionalEnabled && conditionalEnabled.checked) {
            
            const questionRefSelect = q.querySelector('.conditional-question-ref');
            const answerRefSelect = q.querySelector('.conditional-answer-ref');
            
            const oldQuestionValue = questionRefSelect.value;
            const oldAnswerValue = answerRefSelect.value;
            
            updateConditionalQuestionList(q); 
            
            if (Array.from(questionRefSelect.options).some(opt => opt.value === oldQuestionValue)) {
                 questionRefSelect.value = oldQuestionValue;
            }
            
            populateConditionalAnswers(questionRefSelect); 
            
            if (Array.from(answerRefSelect.options).some(opt => opt.value === oldAnswerValue)) {
                answerRefSelect.value = oldAnswerValue;
            }
        }
    });
}

/**
 * Główna funkcja dodająca nową kartę pytania
 */
export function addQuestion(questionText = '', answers = [''], randomize = false, conditional = null, layout = '1-col', questionAnswerGap = 0, answerSpacing = 0, columnWidth = 50) {
    questionCounter++;
    const template = document.getElementById('question-template');
    const clone = template.content.cloneNode(true);
    
    const questionCard = clone.querySelector('[data-question-id]');
    questionCard.setAttribute('data-question-id', questionCounter);
    
    const questionTextarea = clone.querySelector('.question-text');
    if (questionText) {
        questionTextarea.value = questionText;
    }
    
    questionTextarea.addEventListener('input', () => {
        autoResizeTextarea(questionTextarea);
        updatePreview();
    });
    
    if (randomize) {
        clone.querySelector('.randomize-answers').checked = true;
    }
    
    if (layout) {
        clone.querySelector('.answers-layout').value = layout;
    }

    const sliderContainer = clone.querySelector('.column-width-slider-container');
    if (layout === '2-col') {
        sliderContainer.style.display = 'block';
    }
    const colSlider = clone.querySelector('.column-width-slider');
    colSlider.value = columnWidth;
    clone.querySelector('.column-width-value').textContent = `${columnWidth}% / ${100 - columnWidth}%`;

    const gapSlider = clone.querySelector('.question-answer-gap-slider');
    const gapValueSpan = clone.querySelector('.question-answer-gap-value');
    const spacingSlider = clone.querySelector('.answer-spacing-slider');
    const spacingValueSpan = clone.querySelector('.answer-spacing-value');
    
    gapSlider.value = questionAnswerGap;
    gapValueSpan.textContent = `${questionAnswerGap}px`;
    spacingSlider.value = answerSpacing;
    spacingValueSpan.textContent = `${answerSpacing}px`;

    controls.questionsContainer.appendChild(clone);
    
    const addedCard = controls.questionsContainer.lastElementChild;
    autoResizeTextarea(addedCard.querySelector('.question-text'));
    
    if (conditional) {
        const conditionalCheckbox = addedCard.querySelector('.conditional-enabled');
        conditionalCheckbox.checked = true;
        toggleConditionalLogic(conditionalCheckbox);
        
        const questionRefSelect = addedCard.querySelector('.conditional-question-ref');
        questionRefSelect.value = conditional.questionId;
        
        populateConditionalAnswers(questionRefSelect);
        
        addedCard.querySelector('.conditional-answer-ref').value = conditional.answer;
    }
    
    answers.forEach(answerText => {
        addAnswerToQuestion(addedCard, answerText);
    });

    renumberQuestions();
}

/**
 * Usuwa kartę pytania
 */
export function removeQuestion(button) {
    const questionCard = button.closest('[data-question-id]');
    questionCard.remove();
    renumberQuestions();
    updatePreview();
}

/**
 * Przenumerowuje pytania po usunięciu/dodaniu/przesunięciu
 */
export function renumberQuestions() {
    questionCounter = 0;
    const questions = controls.questionsContainer.querySelectorAll('[data-question-id]');
    questions.forEach((q, index) => {
        questionCounter++;
        q.querySelector('.question-number').textContent = questionCounter;
        q.setAttribute('data-question-id', questionCounter);

        q.querySelector('.move-question-up-btn').disabled = (index === 0);
        q.querySelector('.move-question-down-btn').disabled = (index === questions.length - 1);
    });
    updateAllConditionalLists();
}

/**
 * Dodaje pole odpowiedzi (wywoływane przez przycisk)
 */
export function addAnswer(button) {
    const questionCard = button.closest('[data-question-id]');
    addAnswerToQuestion(questionCard);
    updatePreview();
}

/**
 * Usuwa pole odpowiedzi (wywoływane przez przycisk)
 */
export function removeAnswer(button) {
    const answerDiv = button.closest('.answer-input');
    answerDiv.remove();
    updatePreview();
}

/**
 * Przełącza widoczność manualnych kontrolek
 */
export function toggleManualSizing() {
    const manualControls = document.getElementById('manual-controls');
    if (controls.manualSizing.checked) {
        manualControls.style.display = 'grid';
    } else {
        manualControls.style.display = 'none';
    }
}

/**
 * Aktualizuje wyświetlaną wartość suwaka
 */
export function updateSliderValue(sliderId, value) {
    const valueSpan = document.getElementById(`${sliderId}-value`);
    if (valueSpan) {
        valueSpan.textContent = `${value}px`;
    }
}

/**
 * Przełącza widoczność logiki warunkowej
 */
export function toggleConditionalLogic(checkbox) {
    const questionCard = checkbox.closest('[data-question-id]');
    const conditionalSettings = questionCard.querySelector('.conditional-settings');
    
    if (checkbox.checked) {
        conditionalSettings.style.display = 'grid';
        updateConditionalQuestionList(questionCard);
        populateConditionalAnswers(questionCard.querySelector('.conditional-question-ref'));
    } else {
        conditionalSettings.style.display = 'none';
    }
    updatePreview();
}

/**
 * Przesuwa pytanie o jedno miejsce w górę
 */
export function moveQuestionUp(button) {
    const questionCard = button.closest('[data-question-id]');
    const prevSibling = questionCard.previousElementSibling;
    
    if (prevSibling) {
        controls.questionsContainer.insertBefore(questionCard, prevSibling);
        renumberQuestions();
        updatePreview();
    }
}

/**
 * Przesuwa pytanie o jedno miejsce w dół
 */
export function moveQuestionDown(button) {
    const questionCard = button.closest('[data-question-id]');
    const nextSibling = questionCard.nextElementSibling;
    
    if (nextSibling) {
        controls.questionsContainer.insertBefore(nextSibling, questionCard);
        renumberQuestions();
        updatePreview();
    }
}