/* --- BRUTALIST CORE STYLES --- */
:root {
    --color-primary: #0b0c08;
    --color-secondary: #adbead;
    --color-accent: #d35a44; /* Electric Cyan */
    --color-border: 2px solid var(--color-primary);
    --color-bg-light: #ededdb; /* Industrial Grey */
    --font-main: 'Inter', sans-serif;
    --font-mono: '<PERSON><PERSON>', 'Monaco', monospace;
}

body { 
    font-family: var(--font-main); 
    background-color: var(--color-bg-light);
    color: var(--color-primary);
    transition: background-color 0.1s;
}

/* Używamy Menlono dla nagłówków i akcentów */
.brutalist-title {
    font-family: var(--font-mono);
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

/* SCROLLBAR - Minimalistycznie (Z POPRAWKĄ DLA SAFARI I TEXTAREA) */
/* Musimy by<PERSON> bard<PERSON>j pre<PERSON>zy<PERSON>i dla <PERSON> */
body::-webkit-scrollbar,
#controls-panel::-webkit-scrollbar,
#preview-container::-webkit-scrollbar,
textarea::-webkit-scrollbar { 
    width: 4px; 
    height: 4px; /* Dodane dla scrollbarów poziomych, jeśli wystąpią */
}

body::-webkit-scrollbar-thumb,
#controls-panel::-webkit-scrollbar-thumb,
#preview-container::-webkit-scrollbar-thumb,
textarea::-webkit-scrollbar-thumb { 
    background: var(--color-primary); 
}

body::-webkit-scrollbar-track,
#controls-panel::-webkit-scrollbar-track,
#preview-container::-webkit-scrollbar-track,
textarea::-webkit-scrollbar-track { 
    background: var(--color-bg-light); 
}


/* KARTY I PANELE */
.brutalist-card {
    background-color: var(--color-secondary);
    border: var(--color-border);
    box-shadow: 4px 4px 0 0 var(--color-primary);
    transition: box-shadow 0.1s, transform 0.1s;
}

.brutalist-card:hover {
    box-shadow: 6px 6px 0 0 var(--color-primary);
    transform: translate(-2px, -2px);
}

/* PRZYCISKI - Stark Contrast */
.brutalist-btn {
    border: var(--color-border);
    padding: 8px 16px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.1s linear;
    box-shadow: 2px 2px 0 0 var(--color-primary);
    line-height: 1.25;
}

.brutalist-btn-primary {
    background-color: var(--color-primary);
    color: var(--color-accent);
}
.brutalist-btn-primary:hover {
    background-color: var(--color-accent);
    color: var(--color-primary);
    box-shadow: 4px 4px 0 0 var(--color-primary);
    transform: translate(-2px, -2px);
}

.brutalist-btn-secondary {
    background-color: var(--color-secondary);
    color: var(--color-primary);
}
.brutalist-btn-secondary:hover {
    background-color: var(--color-primary);
    color: var(--color-secondary);
    box-shadow: 4px 4px 0 0 var(--color-accent);
    transform: translate(-2px, -2px);
}

/* NOWOŚĆ: Dedykowana klasa dla przycisku akcentu (Importuj) */
.brutalist-btn-accent {
    background-color: var(--color-accent);
    color: var(--color-primary); /* Czarny */
}
.brutalist-btn-accent:hover {
    background-color: var(--color-primary);
    color: var(--color-accent);
    box-shadow: 4px 4px 0 0 var(--color-accent);
    transform: translate(-2px, -2px);
}

/* INPUTS I SELECTY */
.brutalist-input {
    border: var(--color-border);
    background-color: var(--color-bg-light);
    padding: 8px;
    font-family: var(--font-mono);
    font-size: 0.875rem; /* text-sm */
    transition: background-color 0.1s;
}
.brutalist-input:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: -2px;
    background-color: var(--color-bg-light);
}

/* NOWOŚĆ: Styl opcji wewnątrz dropdownu (kompromis - nie zadziała w Safari) */
.brutalist-select option {
    background-color: var(--color-bg-light);
    color: var(--color-primary);
    font-family: var(--font-mono);
    padding: 8px; /* Dla spójności z inputami */
}


/* POPRAWA: Styl drop-down (selektorów) */
.brutalist-select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0 !important;
    padding-right: 0px; /* Dodatkowe miejsce na wskaźnik */
    background-image: none;
    background-color: var(--color-bg-light) !important;
    cursor: pointer;
    position: relative; /* Kontener dla wskaźnika */
}

/* Styl kontenera dropdownu i wskaźnik (ptaszek) */
.select-wrapper {
    position: relative;
    display: block;
}

/* KOREKTA POZYCJI PTASZKA */
.select-wrapper::after {
    content: '';
    position: absolute;
    right: 15px;
    top: 50%;
    /* DODANO KOREKTĘ 1px W DÓŁ DLA WYZEROWANIA */
    transform: translateY(-50%) translateY(10px); 
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid var(--color-primary); /* Czarny trójkąt w dół */
    pointer-events: none;
    z-index: 10;
}

/* Pole tekstowe pytania - WŁĄCZAMY AUTORESIZE */
.question-text {
    resize: none; /* Wyłącz domyślny resize */
    overflow: hidden; /* Ukryj scrollbar */
    min-height: 60px; /* Minimalna wysokość startowa */
    height: auto;
}

/* NOWOŚĆ: Style dla pola odpowiedzi (textarea), aby działał autoresize */
.answer-text {
    resize: none;
    overflow: hidden;
    min-height: 42px; /* Domyślna wysokość (jak input) */
    height: auto;
    line-height: 1.5; /* Lepsze dla wieloliniowego tekstu */
}

/* --- BRUTALIST CHECKBOX STYLE --- */
input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 18px; 
    height: 18px; 
    border: 2px solid var(--color-primary);
    background-color: var(--color-secondary);
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
    transition: background-color 0.1s linear, border-color 0.1s linear;
}

input[type="checkbox"]:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

/* POPRAWIONA POZYCJA ZNACZNIKA CHECK */
input[type="checkbox"]:checked::after {
    content: '';
    display: block;
    width: 7px; 
    height: 10px; 
    border: solid var(--color-accent); 
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    position: absolute;
    top: 50%; 
    left: 50%; 
    margin-left: -3px; /* Korekta dla szerokości */
    margin-top: -8px; /* Dalsza koreta dla idealnego centrowania w pionie */
}

/* --- BRUTALIST RANGE SLIDER STYLE --- */
input[type="range"] {
    -webkit-appearance: none; /* Remove default */
    appearance: none;
    width: 100%;
    height: 2px; /* Line thickness */
    background: var(--color-primary); /* Track color */
    outline: none;
    margin: 0; /* Reset default margin */
    padding: 0; /* Reset default padding */
}

/* Thumb for Webkit (Chrome, Safari, Edge) */
input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px; /* Thumb size */
    height: 16px; /* Thumb size */
    background: var(--color-accent); /* Thumb color */
    border: 2px solid var(--color-primary); /* Thumb border */
    cursor: grab;
    margin-top: -7px; /* Align thumb vertically with track */
    box-shadow: 2px 2px 0 0 var(--color-primary); /* Brutalist shadow */
    transition: background-color 0.1s, box-shadow 0.1s, transform 0.1s;
}

input[type="range"]::-webkit-slider-thumb:active {
    cursor: grabbing;
    box-shadow: 4px 4px 0 0 var(--color-primary);
    transform: translate(-1px, -1px);
}

/* Thumb for Firefox */
input[type="range"]::-moz-range-thumb {
    width: 16px; /* Thumb size */
    height: 16px; /* Thumb size */
    background: var(--color-accent); /* Thumb color */
    border: 2px solid var(--color-primary); /* Thumb border */
    cursor: grab;
    box-shadow: 2px 2px 0 0 var(--color-primary); /* Brutalist shadow */
    transition: background-color 0.1s, box-shadow 0.1s, transform 0.1s;
}
input[type="range"]::-moz-range-thumb:active {
    cursor: grabbing;
    box-shadow: 4px 4px 0 0 var(--color-primary);
    transform: translate(-1px, -1px);
}

/* Track for Firefox (optional, to ensure consistency) */
input[type="range"]::-moz-range-track {
    background: var(--color-primary);
    height: 2px;
}

/* --- PREVIEW PANEL SPECIFIC --- */
#preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px; /* Zmniejszone, aby były bliżej */
    /* Usunięto padding, jest teraz w klasie nadrzędnej */
    /* ZMIANA: Usunięto height, rodzic ma teraz flex-grow i overflow */
    overflow-y: auto;
    align-content: flex-start;
    justify-content: flex-start;
}

.iframe-wrapper {
    display: inline-flex;
    flex-direction: column;
    background-color: var(--color-secondary);
    border: var(--color-border);
    box-shadow: 3px 3px 0 0 var(--color-primary);
    transition: all 0.1s ease-out;
    /* Usunięto zaokrąglenia i padding */
    overflow: hidden;
}

.iframe-wrapper:hover {
    box-shadow: 5px 5px 0 0 var(--color-accent);
    transform: translate(-2px, -2px);
}

/* Nagłówek podglądu */
.preview-header {
    background-color: var(--color-primary);
    color: var(--color-accent);
    padding: 6px 10px;
    font-weight: 700;
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
    border-bottom: 1px solid var(--color-accent); /* Brutalistyczny podział */
    font-family: var(--font-mono);
}

/* Kontener dla iframe */
.preview-iframe-container {
    position: relative;
    background: var(--color-bg-light);
    display: inline-block;
    overflow: visible;
}

.question-preview-iframe {
    border: none;
    display: block;
    background: var(--color-secondary);
}

/* Sekcja pytań */
#questions-container .question-card {
    border: none;
    box-shadow: none;
    /* Usunięto padding i margin, kontroluje to rodzic (gap-5) */
    margin-bottom: 0;
}

/* NOWOŚĆ: Klasy do kontroli układu w sekcji [01] */
.config-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16px; /* Przestrzeń między grupami */
}

.config-item {
    flex-grow: 1;
    min-width: 150px; /* Minimalna szerokość, aby etykieta i pole się zmieściły */
}

/* --- NOWE KLASY DLA SPÓJNOŚCI --- */

/* Główny nagłówek aplikacji */
.brutalist-header {
    background-color: var(--color-secondary);
    border-bottom: var(--color-border);
    box-shadow: 0 4px 0 0 var(--color-primary); 
}

/* Prawy panel (kontener podglądu) */
.brutalist-panel-heavy {
     border: 4px solid var(--color-primary);
     background-color: var(--color-bg-light);
}

/* Wewnętrzny obszar na iframe'y */
.brutalist-preview-area {
    background-color: rgba(0,0,0,0.1); /* Zachowujemy styl z Tailwind */
    border: var(--color-border);
    /* ZMIANA: Padding (p-5) jest teraz ustawiony w index.html */
}

/* Wewnętrzna karta w szablonie (np. dla logiki) */
.brutalist-inner-card {
    background-color: var(--color-bg-light);
    border: var(--color-border);
    padding: 0.75rem; /* p-3 */
}

/* --- NOWOŚĆ: Przyciski do zmiany kolejności pytań (ORYGINALNE) --- */
.brutalist-move-btn {
    background-color: var(--color-bg-light);
    border: 1px solid var(--color-primary);
    color: var(--color-primary);
    font-size: 10px;
    line-height: 1;
    padding: 4px 6px;
    cursor: pointer;
    transition: all 0.1s linear;
}

.brutalist-move-btn:hover {
    background-color: var(--color-primary);
    color: var(--color-accent);
}

.brutalist-move-btn:disabled {
    background-color: var(--color-secondary);
    color: var(--color-bg-light);
    border-color: var(--color-secondary);
    cursor: not-allowed;
    opacity: 0.5;
}


/* --- ZMIANA: NOWE STYLE DRAG-AND-DROP --- */

/* 1. Uchwyt do przeciągania (nagłówek karty) */
.question-drag-handle {
    cursor: grab;
    /* Zapobiegaj zaznaczaniu tekstu podczas przeciągania */
    user-select: none;
    -webkit-user-select: none;
    /* ZMIANA: Zapobiegaj domyślnej akcji dotyku (np. scroll) */
    touch-action: none;
}

/* 2. Wrapper na treść (do zwijania) */
.question-content-wrapper {
    max-height: 1500px; /* Wystarczająco duża, aby pomieścić treść */
    overflow: hidden;
    opacity: 1;
    transition: max-height 0.4s ease-out, opacity 0.2s 0.1s ease-out;
}

/* 3. Stan zwinięty (dodawany przez JS) */
.brutalist-card.collapsed .question-content-wrapper {
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease-in, opacity 0.1s ease-in;
}

/* 4. Placeholder (miejsce na upuszczenie) */
.drag-placeholder {
    background: var(--color-bg-light);
    border: 2px dashed var(--color-primary);
    opacity: 0.5;
    margin: 5px 0; /* Dopasuj do 'gap-5' z kontenera pytań */
    /* Animacja dla "animowanego odstępu" */
    transition: height 0.2s ease-out;
    box-shadow: none; /* Bez brutalistycznego cienia */
}

/* 5. Element "duch" (ten, który przeciągamy) */
.brutalist-card.dragging {
    opacity: 0.9;
    box-shadow: 8px 8px 0 0 var(--color-accent);
    /* Lekki efekt "podniesienia" */
    transform: rotate(1deg) scale(1.01);
}

/* 6. Ukryj stare przyciski góra/dół */
.brutalist-move-btn {
    display: none;
}

/* 7. Klasa body blokująca zaznaczanie tekstu (NOWOŚĆ) */
body.is-dragging,
body.is-dragging * {
    user-select: none !important;
    -webkit-user-select: none !important;
}

/* --- NOWOŚĆ: Style dla ekranu ładowania --- */
@keyframes brutalist-spin {
    0% { transform: rotate(0deg); box-shadow: 4px 4px 0 0 var(--color-primary); }
    50% { transform: rotate(180deg); box-shadow: 4px 4px 0 0 var(--color-accent); }
    100% { transform: rotate(360deg); box-shadow: 4px 4px 0 0 var(--color-primary); }
}

.brutalist-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(237, 237, 219, 0.8); /* Półprzezroczyste tło --color-bg-light */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9998;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease-out;
    backdrop-filter: blur(2px);
}

.brutalist-loading-overlay.show {
    opacity: 1;
    pointer-events: all;
}

.brutalist-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-primary);
    background-color: var(--color-secondary);
    animation: brutalist-spin 1s linear infinite;
    z-index: 9999;
}

.brutalist-loading-text {
    font-family: var(--font-mono);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--color-primary);
    margin-top: 1.5rem; /* mt-6 */
    padding: 0.5rem 1rem; /* p-2 px-4 */
    background-color: var(--color-secondary);
    border: var(--color-border);
    box-shadow: 3px 3px 0 0 var(--color-primary);
}