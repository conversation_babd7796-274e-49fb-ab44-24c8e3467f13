// preview.js - Logika generowania podglądu i finalnego banera

import { controls } from './config.js';
import { collectDataFromUI } from './data.js';

// ZMIANA: Przeniesiono szablon z index.html do stałej w JS, aby uniknąć parsowania przez przeglądarkę
const bannerTemplateString = `
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="ad.size" content="width=__BANNER_WIDTH__,height=__BANNER_HEIGHT__">
    <title>Baner <PERSON>wy - __BANNER_WIDTH__ x __BANNER_HEIGHT__</title>
    
    <!-- NOWOŚĆ: Placeholder dla dynamicznych fontów @fontsource -->
    __FONT_IMPORT_LINKS__

    <!-- NOWY SKRYPT CLICKTAG ZGODNY Z GOOGLE -->
    <script type="text/javascript">
        // Ta zmienna zostanie dynamicznie nadpisana przez platformę Google (DCM)
        // Wartość __EXIT_URL__ służy tylko do testów lokalnych.
        var clickTag = "__EXIT_URL__";
    </script>
    <!-- KONIEC SKRYPTU CLICKTAG -->

    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body, html {
            width: __BANNER_WIDTH__px;
            height: __BANNER_HEIGHT__px;
            overflow: hidden;
            font-family: __FONT_FAMILY__;
            /* NOWOŚĆ: Blokowanie zaznaczania tekstu */
            -webkit-user-select: none; /* Safari, Chrome */
            -moz-user-select: none;    /* Firefox */
            -ms-user-select: none;     /* IE/Edge */
            user-select: none;         /* Standard */
            
            /* NOWOŚĆ: Ustawienie domyślnego kursora */
            cursor: default;
        }

        #banner-container {
            position: relative; /* ZMIANA: Kluczowe dla stref absolutnych */
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            z-index: 1;
        }

        /* ----- NOWE STREFY CLICKTAG ----- */
        .clicktag-zone {
            position: absolute;
            left: 0;
            width: 100%;
            z-index: 10; /* Nad treścią */
            cursor: pointer;
            display: none; /* Ukryte na starcie */
            /* Tło do debugowania: */
            /* background: rgba(255, 0, 0, 0.3); */
        }
        #clicktag-top-zone { top: 0; }
        #clicktag-bottom-zone { bottom: 0; }
        /* ----- KONIEC NOWYCH STREF ----- */


        .survey-header {
            text-align: left;
            font-weight: bold;
            padding: var(--header-padding) var(--padding);
            font-size: var(--header-font);
            letter-spacing: 1px;
            flex-shrink: 0;
            background-color: var(--header-bg-color);
            color: var(--header-font-color);
            /* ZMIANA: Już nie jest klikalny, strefa jest NAD nim */
            position: relative;
            z-index: 1; /* Pod strefami */
            cursor: default; /* ZMIANA: Domyślnie nieklikalny */
        }

        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding-left: var(--padding);
            padding-right: var(--padding);
            padding-bottom: var(--padding);
            padding-top: var(--content-top-padding); /* Kontrolowane globalnie */
            overflow: hidden;
            position: relative;
            z-index: 1; /* Pod strefami */
        }

        /* RESPONSIVE SIZING - Dostosowane do formatów */
        .size-300x250 { --question-font: 16px; --answer-font: 13px; --radio-size: 16px; --spacing: 10px; --padding: 15px; --header-font: 10px; --header-padding: 6px; --thankyou-title: 20px; --thankyou-message: 13px; --content-top-padding: 15px; --global-question-answer-gap: 5px; }
        .size-300x600 { --question-font: 20px; --answer-font: 16px; --radio-size: 20px; --spacing: 14px; --padding: 20px; --header-font: 12px; --header-padding: 8px; --thankyou-title: 26px; --thankyou-message: 16px; --content-top-padding: 20px; --global-question-answer-gap: 5px; }
        .size-320x480 { --question-font: 18px; --answer-font: 15px; --radio-size: 18px; --spacing: 12px; --padding: 18px; --header-font: 11px; --header-padding: 7px; --thankyou-title: 23px; --thankyou-message: 15px; --content-top-padding: 18px; --global-question-answer-gap: 5px; }

        /* Użyj manualnych rozmiarów jeśli są ustawione */
        .manual-sizing {
            --question-font: var(--manual-question-font);
            --answer-font: var(--manual-answer-font);
            --radio-size: var(--manual-radio-size);
            --spacing: var(--manual-spacing);
            --padding: var(--manual-padding);
            --header-font: var(--manual-header-font);
            /* POPRAWKA: Prawidłowe nadpisanie zmiennej */
            --header-padding: var(--manual-header-padding); 
            --thankyou-title: var(--manual-thankyou-title);
            --thankyou-message: var(--manual-thankyou-message);
            --content-top-padding: var(--manual-content-top-padding);
            --global-question-answer-gap: var(--manual-global-question-answer-gap);
        }
        
        /* Zmienne dla kolorów */
        :root {
            --answer-hover-color: __ANSWER_HOVER_COLOR__;
            --background-color: __BACKGROUND_COLOR__;
            --header-bg-color: __HEADER_BG_COLOR__;
            --header-font-color: __HEADER_FONT_COLOR__;
            --question-font-color: __QUESTION_FONT_COLOR__;
            --answer-font-color: __ANSWER_FONT_COLOR__;
            --radio-bg-color: __RADIO_BG_COLOR__;
            --radio-border-color: __RADIO_BORDER_COLOR__;
            --radio-dot-color: __RADIO_DOT_COLOR__;
        }

        .question-screen {
            display: none;
            flex-direction: column;
            height: 100%;
            justify-content: flex-start;
            position: relative;
            z-index: 10;
            --question-answer-gap: 15px;
            --answer-spacing: 10px;
        }

        .question-screen.active {
            display: flex;
        }

        .question-text {
            font-size: var(--question-font);
            font-weight: bold;
            margin-bottom: calc(var(--global-question-answer-gap) + var(--question-answer-gap));
            line-height: 1.3;
            text-align: left;
            color: var(--question-font-color);
            /* ZMIANA: Już nie jest klikalny */
        }

        .answers-list {
            display: flex;
            flex-direction: column;
            gap: var(--answer-spacing);
            /* ZMIANA: Marginesy kontrolowane przez JS */
            margin-bottom: 0;
            position: relative; /* ZMIANA: Kluczowe dla stref */
            z-index: 11; /* NAD strefami clicktag */
        }

        .answers-list.layout-2-col {
            display: grid;
            /* ZMIANA: Użyj zmiennych CSS zamiast stałego 1fr 1fr */
            grid-template-columns: var(--col-1-width, 1fr) var(--col-2-width, 1fr);
            gap: var(--answer-spacing);
        }

        .answer-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: calc(var(--spacing) * 0.6) 5px;
            border-radius: 6px;
            transition: background-color 0.2s;
            position: relative;
            z-index: 10; /* Bez zmian, ale upewnijmy się, że jest nad z-index: 1 */
            cursor: pointer; /* ZMIANA: Dodano cursor pointer dla spójności */
        }

        .answer-option:hover {
            background-color: var(--answer-hover-color);
        }

        .radio-button {
            width: var(--radio-size);
            height: var(--radio-size);
            border: 2px solid var(--radio-border-color);
            background-color: var(--radio-bg-color);
            border-radius: 50%;
            flex-shrink: 0;
            position: relative;
            transition: all 0.2s;
        }

        .answer-option:hover .radio-button {
            border-color: var(--radio-dot-color);
        }

        .radio-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            width: 60%;
            height: 60%;
            background-color: var(--radio-dot-color);
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .answer-option.selected .radio-button {
            border-color: var(--radio-dot-color);
        }

        .answer-option.selected .radio-button::after {
            transform: translate(-50%, -50%) scale(1);
        }

        .answer-text {
            font-size: var(--answer-font);
            line-height: 1.4;
            flex: 1;
            text-align: left;
            color: var(--answer-font-color);
        }

        .thank-you-screen {
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            cursor: pointer; /* Ekran końcowy pozostaje prosty */
            position: relative; /* ZMIANA: Dodane dla z-index */
            z-index: 12; /* Na samym wierzchu, gdy aktywny */
        }

        .thank-you-screen.active {
            display: flex;
        }

        .thank-you-title {
            font-size: var(--thankyou-title);
            font-weight: bold;
            color: var(--question-font-color);
            margin-bottom: calc(var(--spacing) * 1.5);
        }

        .thank-you-message {
            font-size: var(--thankyou-message);
            color: var(--answer-font-color);
            line-height: 1.6;
        }

        .welcome-screen {
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            position: relative;
            z-index: 12;
        }

        .welcome-screen.active {
            display: flex;
        }

        .welcome-title {
            font-size: var(--welcome-title-size);
            font-weight: bold;
            color: var(--question-font-color);
            margin-bottom: 20px;
        }

        .welcome-button {
            font-size: var(--welcome-button-font-size);
            padding: var(--welcome-button-padding);
            background-color: var(--header-bg-color);
            color: var(--header-font-color);
            border: none;
            cursor: pointer;
            border-radius: 6px;
        }

        /* Animacje */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .question-screen.active, .thank-you-screen.active, .welcome-screen.active {
            animation: fadeIn 0.4s ease-out;
        }

        /* ZMIANA: .answers-list jest teraz na wierzchu */
        .answers-list {
            position: relative;
            z-index: 11;
        }
    </style>
</head>
<body style="background-color: transparent;">
    <!-- ZMIANA: Dodano nowe strefy clicktag na poziomie #banner-container -->
    <div id="banner-container" class="size-__BANNER_WIDTH__x__BANNER_HEIGHT____MANUAL_SIZING_CLASS__" style="__MANUAL_SIZING_VARS__; background-color: var(--background-color);">
        
        <div id="clicktag-top-zone" class="clicktag-zone"></div>
        <div id="clicktag-bottom-zone" class="clicktag-zone"></div>

        <div class="survey-header">BADANIE ANKIETOWE</div>
        
        <div class="content-area">
            <div class="welcome-screen" id="welcome">
                <div class="welcome-title">__WELCOME_TITLE__</div>
                <button class="welcome-button">__WELCOME_BUTTON_TEXT__</button>
            </div>
            <div class="thank-you-screen" id="thank-you">
                <div class="thank-you-title">__THANK_YOU_TITLE__</div>
                <div class="thank-you-message">__THANK_YOU_MESSAGE__</div>
            </div>
        </div>
    </div>

    <!-- NOWY SKRYPT DO WYSYŁANIA DANYCH -->
    <script type="text/javascript">
      document.addEventListener("DOMContentLoaded", function() {
        const bannerSize = "__BANNER_SIZE_STRING__"; // ZASTĘPOWANE
        var uniqueId = generateUUID(); // Generate unique ID for this session
        const endpoint = "__ENDPOINT_URL__"; // ZASTĘPOWANE

        // Send unique ID and banner size to the server
        fetch(endpoint, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              action: "init",
              unique_id: uniqueId,
              banner_size: bannerSize
            })
          })
          .then(response => response.json())
          .then(data => console.log('Init:', data));

        // Function to save an answer
        window.safeAnswer = function(questionNumber, answer) {
          fetch(endpoint, {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                action: "save_answer",
                unique_id: uniqueId,
                question: \`question_\${questionNumber}\`,
                answer: answer
              })
            })
            .then(response => response.json())
            .then(data => console.log('Answer Saved:', data));
        };

        // Function to generate a UUID
        function generateUUID() {
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
          });
        }
      });
    </script>
    <!-- KONIEC NOWEGO SKRYPTU -->

    <script>
        // Wczytaj dane ankiety
        const surveyData = __SURVEY_DATA_JSON__;
        const endpointUrl = "__ENDPOINT_URL__";
        let activeQuestion = 0; // Start with 0 to show welcome screen
        const userAnswers = {};
        const questionNumbering = __QUESTION_NUMBERING__;

        const bannerContainer = document.querySelector('.content-area');
        const welcomeScreen = document.getElementById('welcome');
        const thankYouScreen = document.getElementById('thank-you');

        // Funkcja renderująca pytania
        function renderQuestions() {
            surveyData.questions.forEach((question, index) => {
                const questionNumber = index + 1;
                const screen = document.createElement('div');
                screen.className = 'question-screen';
                screen.id = \`question-\${questionNumber}\`;
                
                screen.style.setProperty('--question-answer-gap', \`\${question.questionAnswerGap || 0}px\`);
                screen.style.setProperty('--answer-spacing', \`\${question.answerSpacing || 10}px\`);
                
                if (question.conditional) {
                    screen.dataset.conditional = JSON.stringify(question.conditional);
                }
                
                const questionText = document.createElement('div');
                questionText.className = 'question-text';
                if (questionNumbering) {
                    questionText.innerHTML = \`\${questionNumber}. \${question.text}\`;
                } else {
                    questionText.innerHTML = question.text;
                }
                screen.appendChild(questionText);

                const answersList = document.createElement('div');
                answersList.className = 'answers-list';

                if (question.layout === '2-col') {
                    answersList.classList.add('layout-2-col');
                    // NOWA LOGIKA: Ustaw proporcje kolumn
                    const colWidth = question.columnWidth || 50;
                    answersList.style.setProperty('--col-1-width', \`\${colWidth}%\`);
                    answersList.style.setProperty('--col-2-width', \`\${100 - colWidth}%\`);
                }

                let answerIndices = question.answers.map((_, idx) => idx);
                
                if (question.randomize) {
                    answerIndices = shuffleArray(answerIndices);
                }

                answerIndices.forEach((originalIndex) => {
                    const answer = question.answers[originalIndex];
                    
                    const option = document.createElement('div');
                    option.className = 'answer-option';
                    option.dataset.answer = answer;
                    option.dataset.questionId = questionNumber;
                    
                    option.innerHTML = \`
                        <div class="radio-button"></div>
                        <div class="answer-text">\${answer}</div>
                    \`;

                    option.addEventListener('click', (e) => {
                        e.stopPropagation();
                        selectAnswer(questionNumber, answer, option);
                    });

                    answersList.appendChild(option);
                });

                screen.appendChild(answersList);
                bannerContainer.insertBefore(screen, document.getElementById('thank-you'));
            });

            if (activeQuestion > 0) {
                showQuestion(activeQuestion);
            } else {
                showWelcomeScreen();
            }
        }

        function showWelcomeScreen() {
            document.querySelectorAll('.question-screen').forEach(s => s.classList.remove('active'));
            thankYouScreen.classList.remove('active');
            welcomeScreen.classList.add('active');
        }

        // --- POPRAWIONA FUNKCJA ---
        function selectAnswer(questionId, answer, optionElement) {
            userAnswers[\`q\${questionId}\`] = answer;

            if (typeof window.safeAnswer === 'function') {
                window.safeAnswer(questionId, answer);
            }

            const screen = document.getElementById(\`question-\${questionId}\`);
            screen.querySelectorAll('.answer-option').forEach(opt => opt.classList.remove('selected'));
            optionElement.classList.add('selected');

            setTimeout(() => {
                const nextQuestion = findNextQuestion(questionId);
                if (nextQuestion !== null) {
                    showQuestion(nextQuestion);
                } else {
                    finishSurvey();
                }
            }, 500);
        }
        // --- KONIEC POPRAWIONEJ FUNKCJI ---

        function findNextQuestion(currentQuestionId) {
            for (let i = currentQuestionId; i < surveyData.questions.length; i++) {
                const nextQuestionId = i + 1;
                const screen = document.getElementById(\`question-\${nextQuestionId}\`);
                
                if (!screen) return null;
                
                if (screen.dataset.conditional) {
                    const condition = JSON.parse(screen.dataset.conditional);
                    const requiredAnswer = userAnswers[\`q\${condition.questionId}\`];
                    
                    if (requiredAnswer === condition.answer) {
                        return nextQuestionId;
                    }
                } else {
                    return nextQuestionId;
                }
            }
            return null;
        }
        
        // --- POPRAWIONA LOGIKA STREF - PRZENIESIONA TUTAJ ---
        const bannerContainerElement = document.getElementById('banner-container');
        const topZone = document.getElementById('clicktag-top-zone');
        const bottomZone = document.getElementById('clicktag-bottom-zone');
        const surveyHeader = document.querySelector('.survey-header'); // ZMIANA: Pobierz nagłówek

        /**
         * Funkcja do dynamicznego ustawiania stref klikalnych
         */
        function updateClickZones() {
            const activeScreen = document.querySelector('.question-screen.active');
            
            if (!activeScreen) {
                // Jeśli nie ma aktywnego pytania (np. błąd), ukryj strefy
                topZone.style.display = 'none';
                bottomZone.style.display = 'none';
                return;
            }

            const answersList = activeScreen.querySelector('.answers-list');
            if (!answersList) return; // Na wszelki wypadek

            // Użyj małego opóźnienia (0ms) aby poczekać na renderowanie
            setTimeout(() => {
                const containerRect = bannerContainerElement.getBoundingClientRect();
                const answersRect = answersList.getBoundingClientRect();
                
                // ZMIANA: Dwa różne odstępy
                const gapTop = 50; // Wymagany odstęp 20px na górze
                const gapBottom = 30; // Standardowy odstęp 10px na dole

                // Ustaw strefę GÓRNĄ
                // Od góry kontenera do (pozycji odpowiedzi - odstęp)
                const topZoneHeight = answersRect.top - containerRect.top - gapTop;
                topZone.style.height = \`\${topZoneHeight > 0 ? topZoneHeight : 0}px\`;
                topZone.style.display = 'block';

                // Ustaw strefę DOLNĄ
                // Od (pozycji końca odpowiedzi + odstęp) do końca kontenera
                const bottomZoneTop = answersRect.bottom - containerRect.top + gapBottom;
                const bottomZoneHeight = containerRect.height - bottomZoneTop;
                
                bottomZone.style.top = \`\${bottomZoneTop}px\`;
                bottomZone.style.height = \`\${bottomZoneHeight > 0 ? bottomZoneHeight : 0}px\`;
                bottomZone.style.display = 'block';
            }, 0); // 0ms wystarczy, aby poczekać na następną klatkę renderowania
        }

        function showQuestion(questionNumber) {
            document.querySelectorAll('.question-screen').forEach(s => s.classList.remove('active'));
            thankYouScreen.classList.remove('active');
            welcomeScreen.classList.remove('active');
            
            const screen = document.getElementById(\`question-\${questionNumber}\`);
            if (screen) {
                screen.classList.add('active');
                // Zaktualizuj strefy PO tym, jak ekran stał się widoczny
                updateClickZones(); 
            }
            
            // ZMIANA: Zarządzanie kursorami
            surveyHeader.style.cursor = 'default';
            thankYouScreen.style.cursor = 'default';
        }

        function finishSurvey() {
            document.querySelectorAll('.question-screen').forEach(s => s.classList.remove('active'));
            welcomeScreen.classList.remove('active');
            thankYouScreen.classList.add('active');
            
            // Ukryj strefy absolutne
            topZone.style.display = 'none';
            bottomZone.style.display = 'none';
            
            // ZMIANA: Przywróć kursor dla nagłówka i ekranu końcowego
            surveyHeader.style.cursor = 'pointer';
            thankYouScreen.style.cursor = 'pointer';
        }
        // --- KONIEC POPRAWIONEJ LOGIKI STREF ---

        function shuffleArray(array) {
            const arr = array.slice();
            for (let i = arr.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [arr[i], arr[j]] = [arr[j], arr[i]];
            }
            return arr;
        }

        if (surveyData.questions && surveyData.questions.length > 0) {
            renderQuestions();
        } else {
            bannerContainer.innerHTML = '<div style="display:flex;align-items:center;justify-content:center;height:100%;"><p style="color:#ef4444;font-weight:bold;">Brak danych ankiety!</p></div>';
        }

        // --- LISTENERY CLICKTAG ---
        
        // Funkcja do otwierania clickTag
        function openClickTag() {
            if (window.clickTag && window.clickTag !== '#') {
                window.open(window.clickTag, '_blank');
            }
        }

        // 1. Listenery dla nowych stref (pytania)
        topZone.addEventListener('click', openClickTag);
        bottomZone.addEventListener('click', openClickTag);

        // 2. ZMIANA: Listenery dla ekranu końcowego (pokrycie całego banera)
        thankYouScreen.addEventListener('click', () => {
            if (thankYouScreen.classList.contains('active')) {
                openClickTag();
            }
        });

        surveyHeader.addEventListener('click', () => {
            if (thankYouScreen.classList.contains('active')) {
                openClickTag();
            }
        });

        document.querySelector('.welcome-button').addEventListener('click', () => {
            showQuestion(1);
        });
        // --- KONIEC LISTENERÓW ---
    </script>
</body>
</html>
`;
// KONIEC ZMIANY

/**
 * NOWOŚĆ: Główna funkcja generująca samowystarczalny baner (Metoda 3)
 * Pobiera fonty, konwertuje na Base64 i osadza je.
 */
export async function generateSelfContainedBanner() {
    const { settings, surveyData } = collectDataFromUI();
    let embeddedFontData = null; // Domyślnie null

    if (settings.fontPackageName) {
        // 1. Zbuduj nazwy CSS i URL-e
        const fontCSSName = settings.fontFamily.split(',')[0].replace(/'/g, ''); // Pobierz 'Lato' z "'Lato', Arial, sans-serif"
        
        const cssUrls = [
            `https://cdn.jsdelivr.net/npm/@fontsource/${settings.fontPackageName}@5/400-latin.min.css`,
            `https://cdn.jsdelivr.net/npm/@fontsource/${settings.fontPackageName}@5/400-latin-ext.min.css`,
            `https://cdn.jsdelivr.net/npm/@fontsource/${settings.fontPackageName}@5/700-latin.min.css`,
            `https://cdn.jsdelivr.net/npm/@fontsource/${settings.fontPackageName}@5/700-latin-ext.min.css`
        ];

        // 2. Utwórz funkcję pomocniczą do pobierania i konwersji
        const fetchAndEmbedFont = async (cssUrl, weight) => {
            // Pobierz plik CSS
            const cssResponse = await fetch(cssUrl);
            if (!cssResponse.ok) throw new Error(`Nie można pobrać CSS: ${cssUrl}`);
            const cssText = await cssResponse.text();
            
            // ***** KLUCZOWA POPRAWKA *****
            // Użyj `cssResponse.url` (końcowy URL po przekierowaniu),
            // a nie `cssUrl` (oryginalny URL)
            const finalCssUrl = cssResponse.url; 
            // ***** KONIEC POPRAWKI *****

            // Znajdź URL do pliku .woff2 wewnątrz CSS
            const fontUrlMatch = cssText.match(/url\((.*?\.woff2)\)/);
            // Znajdź unicode-range
            const unicodeRangeMatch = cssText.match(/unicode-range:\s*(.*?);/);
            
            if (!fontUrlMatch || !fontUrlMatch[1] || !unicodeRangeMatch || !unicodeRangeMatch[1]) {
                // Zignoruj błąd jeśli plik to tylko import (np. 400.min.css)
                console.warn(`Nie znaleziono fontu/unicode-range w: ${cssUrl}. Prawdopodobnie to plik importu.`);
                return null; // Zwróć null, aby go odfiltrować
            }
            
            const relativeFontUrl = fontUrlMatch[1];
            const unicodeRange = unicodeRangeMatch[1];

            // Stwórz absolutny URL do pliku fontu używając `finalCssUrl`
            const absoluteFontUrl = new URL(relativeFontUrl, finalCssUrl).href;

            // Pobierz plik fontu (jako binarny blob)
            const fontResponse = await fetch(absoluteFontUrl);
            if (!fontResponse.ok) throw new Error(`Nie można pobrać fontu: ${absoluteFontUrl}`);
            
            const fontBlob = await fontResponse.blob(); // Pobierz jako blob

            // Zawiń FileReader w Promise, aby działał z async/await
            const base64data = await new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onloadend = () => {
                    if (reader.result) {
                        const base64data = reader.result.toString().split(',')[1];
                        resolve(base64data);
                    } else {
                        reject(new Error("FileReader nie zwrócił wyniku."));
                    }
                };
                reader.onerror = (error) => {
                    console.error("Błąd FileReader:", error);
                    reject(error);
                };
                reader.readAsDataURL(fontBlob);
            });
            
            // Zwróć kompletny obiekt danych
            return {
                weight: weight,
                base64data: base64data,
                unicodeRange: unicodeRange
            };
        };

        // 3. Pobierz obie wagi fontu równolegle
        try {
            // ZMIANA: Pobierz wszystkie 4 pliki równolegle
            const fontDataResults = await Promise.all([
                fetchAndEmbedFont(cssUrls[0], 400), // 400-latin
                fetchAndEmbedFont(cssUrls[1], 400), // 400-latin-ext
                fetchAndEmbedFont(cssUrls[2], 700), // 700-latin
                fetchAndEmbedFont(cssUrls[3], 700)  // 700-latin-ext
            ]);
            
            // Odfiltruj wyniki 'null' (jeśli jakieś wystąpiły)
            const validFontData = fontDataResults.filter(data => data !== null);

            if (validFontData.length === 0) {
                 throw new Error("Nie udało się pobrać żadnych prawidłowych danych fontów.");
            }

            embeddedFontData = { 
                fontData: validFontData, // Tablica 2 lub 4 obiektów
                fontCSSName: fontCSSName
            };

        } catch (error) {
            console.error("Błąd podczas osadzania fontów:", error);
            embeddedFontData = null; // Użyj logiki fallback
        }
    }

    // 4. Wygeneruj finalny HTML, przekazując pobrane dane (lub null)
    return generateBanner(0, embeddedFontData);
}


/**
 * Aktualizuje podglądy iframe dla wszystkich pytań (Metoda 2 - szybki podgląd)
 */
export function updatePreview() {
    const { settings, surveyData } = collectDataFromUI();
    
    controls.previewContainer.innerHTML = '';

    const width = parseInt(settings.bannerWidth);
    const height = parseInt(settings.bannerHeight);

    if (surveyData.questions.length === 0) {
        controls.previewContainer.innerHTML = `
            <div class="w-full p-10 text-center">
                <div class="bg-white border-2 border-dashed border-black p-8 brutalist-card inline-block transition-none shadow-none">
                    <p class="text-xl font-black brutalist-title text-black mb-1">BRAK DANYCH</p>
                    <p class="text-xs font-mono text-black">DODAJ PYTANIE ABY AKTYWOWAĆ PODGLĄD.</p>
                </div>
            </div>
        `;
        return;
    }

    const maxPreviewWidth = 500; 
    const maxPreviewHeight = 700; 
    
    let scale = 1;
    let previewWidth = width;
    let previewHeight = height;
    
    if (width > maxPreviewWidth || height > maxPreviewHeight) {
        const scaleX = maxPreviewWidth / width;
        const scaleY = maxPreviewHeight / height;
        scale = Math.min(scaleX, scaleY);
        previewWidth = Math.floor(width * scale);
        previewHeight = Math.floor(height * scale);
    }

    // Renderuj wszystkie pytania
    surveyData.questions.forEach((question, index) => {
        const questionNumber = index + 1;
        
        // ZMIANA: Zawsze wywołuj z 'null' dla podglądu, aby użyć szybkiego linkowania
        const bannerHtml = generateBanner(questionNumber, null); 
        
        const wrapper = document.createElement('div');
        wrapper.className = 'iframe-wrapper';
        
        const conditionalText = question.conditional 
            ? `IF(Q${question.conditional.questionId}=${question.conditional.answer.replace(/<br>/g, ' ')})`
            : '';
            
        const conditionalBadge = question.conditional 
            ? `<span style="background: var(--color-accent); color: var(--color-primary); padding: 2px 5px; font-size: 10px; margin-left: 8px; font-weight: bold; display: inline-block; max-width: 100px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: middle;" title="${conditionalText}">${conditionalText}</span>`
            : '';
            
        const randomBadge = question.randomize 
            ? `<span style="background: var(--color-secondary); color: var(--color-primary); padding: 2px 5px; font-size: 10px; margin-left: 8px; font-weight: bold; display: inline-block; vertical-align: middle;">RANDOM</span>` 
            : '';
        
        const header = document.createElement('div');
        header.className = 'preview-header';
        const scaleInfo = scale < 1 ? ` <span style="opacity: 0.7; font-size: 9px;">SCALE: ${Math.round(scale * 100)}%</span>` : '';
        
        header.innerHTML = `
            <span style="flex-shrink: 0;">Q${questionNumber} | ${width}x${height}${scaleInfo}</span>
            <span style="min-width: 0; text-align: right;">${randomBadge}${conditionalBadge}</span>
        `;
        wrapper.appendChild(header);
        
        const iframeContainer = document.createElement('div');
        iframeContainer.className = 'preview-iframe-container';
        iframeContainer.style.width = `${previewWidth}px`;
        iframeContainer.style.height = `${previewHeight}px`;
        
        const iframe = document.createElement('iframe');
        iframe.className = 'question-preview-iframe';
        iframe.style.width = `${width}px`;
        iframe.style.height = `${height}px`;
        iframe.style.transform = `scale(${scale})`;
        iframe.style.transformOrigin = 'top left';
        iframe.setAttribute('scrolling', 'no');
        iframe.srcdoc = bannerHtml;
        
        iframeContainer.appendChild(iframe);
        wrapper.appendChild(iframeContainer);
        controls.previewContainer.appendChild(wrapper);
    });

    // Dodaj podgląd ekranu końcowego
    const thankYouWrapper = document.createElement('div');
    thankYouWrapper.className = 'iframe-wrapper';
    
    const thankYouHeader = document.createElement('div');
    thankYouHeader.className = 'preview-header';
    thankYouHeader.style.background = 'var(--color-primary)';
    thankYouHeader.style.color = 'var(--color-accent)';
    const scaleInfo = scale < 1 ? ` <span style="opacity: 0.7; font-size: 9px;">SCALE: ${Math.round(scale * 100)}%</span>` : '';
    thankYouHeader.innerHTML = `<span>:: FINAL SCREEN :: ${width}x${height}${scaleInfo}</span>`;
    thankYouWrapper.appendChild(thankYouHeader);
    
    const thankYouContainer = document.createElement('div');
    thankYouContainer.className = 'preview-iframe-container';
    thankYouContainer.style.width = `${previewWidth}px`;
    thankYouContainer.style.height = `${previewHeight}px`;
    
    const thankYouBannerHtml = generateBannerThankYouOnly();
    const thankYouIframe = document.createElement('iframe');
    thankYouIframe.className = 'question-preview-iframe';
    thankYouIframe.style.width = `${width}px`;
    thankYouIframe.style.height = `${height}px`;
    thankYouIframe.style.transform = `scale(${scale})`;
    thankYouIframe.style.transformOrigin = 'top left';
    thankYouIframe.setAttribute('scrolling', 'no');
    thankYouIframe.srcdoc = thankYouBannerHtml;
    
    thankYouContainer.appendChild(thankYouIframe);
    thankYouWrapper.appendChild(thankYouContainer);
    controls.previewContainer.appendChild(thankYouWrapper);
}

/**
 * Generuje kod HTML finalnego banera
 * @param {number} questionNumber - 0 dla finalnego banera, 1+ dla podglądu konkretnego pytania
 * @param {object} embedData - NOWOŚĆ: Obiekt zawierający dane Base64 i unicode-range
 */
export function generateBanner(questionNumber = 0, embedData = null) {
    const { settings, surveyData } = collectDataFromUI();

    if (surveyData.questions.length === 0 && questionNumber !== -1) {
        return null;
    }

    let template = bannerTemplateString.trim();

    let manualSizingClass = '';
    let manualSizingVars = '';
    
    if (settings.manualSizing) {
        manualSizingClass = ' manual-sizing';
        manualSizingVars = `
            --manual-question-font: ${settings.questionFontSize}px;
            --manual-answer-font: ${settings.answerFontSize}px;
            --manual-radio-size: ${settings.radioSize}px;
            --manual-spacing: ${settings.spacingSize}px;
            --manual-padding: ${settings.paddingSize}px;
            --manual-header-font: ${settings.headerTextSize}px;
            --manual-header-padding: ${settings.headerPadding}px;
            --manual-thankyou-title: ${settings.thankYouTitleSize}px;
            --manual-thankyou-message: ${settings.thankYouMessageSize}px;
            --manual-content-top-padding: ${settings.contentTopPadding}px;
            --manual-global-question-answer-gap: ${settings.globalQuestionAnswerGap}px;
            --welcome-title-size: ${settings.welcomeTitleSize}px;
            --welcome-button-font-size: ${settings.welcomeButtonFontSize}px;
            --welcome-button-padding: ${settings.welcomeButtonPadding}px;
        `.trim();
    }

    // --- ZAKTUALIZOWANA LOGIKA FONTÓW ---
    let fontImportLinks = '';
    let finalFontFamily = settings.fontFamily; // Domyślnie fallback

    // ZMIANA: Logika budowania 4 reguł @font-face
    if (embedData && embedData.fontData && embedData.fontData.length > 0) {
        // SCENARIUSZ 1: Osadzanie Base64 (Finalne generowanie)
        const fontCSSName = embedData.fontCSSName;
        // Użyj 'Arial, sans-serif' jako twardego fallbacka
        finalFontFamily = `'${fontCSSName}', Arial, sans-serif`; 
        
        // Zbuduj bloki @font-face
        const fontFaceRules = embedData.fontData.map(font => {
            return `
        @font-face {
            font-family: '${fontCSSName}';
            font-style: normal;
            font-weight: ${font.weight};
            font-display: block;
            src: url(data:font/woff2;base64,${font.base64data}) format('woff2');
            unicode-range: ${font.unicodeRange};
        }`;
        }).join('');

        fontImportLinks = `
    <style>${fontFaceRules}
    </style>`;
        
    } else if (settings.fontPackageName) {
        // SCENARIUSZ 2: Linkowanie do CDN (Podgląd na żywo)
        // Ta logika jest poprawna - linkujemy do GŁÓWNYCH plików CSS
        finalFontFamily = settings.fontFamily; // Już zawiera fallback
        
        // Używamy głównych plików, które same importują podzbiory
        const url400 = `https://cdn.jsdelivr.net/npm/@fontsource/${settings.fontPackageName}@5/400.min.css`;
        const url700 = `https://cdn.jsdelivr.net/npm/@fontsource/${settings.fontPackageName}@5/700.min.css`;
        
        fontImportLinks = `
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="stylesheet" href="${url400}">
    <link rel="stylesheet" href="${url700}">`;
    }
    // SCENARIUSZ 3: (Bezpieczny font) - `fontImportLinks` pozostaje pusty, `finalFontFamily` jest poprawny.
    // --- KONIEC LOGIKI FONTÓW ---

    const bannerSizeString = `${settings.bannerWidth}x${settings.bannerHeight}`;

    template = template.replace('__SURVEY_DATA_JSON__', JSON.stringify(surveyData, null, 2));
    template = template.replace(new RegExp('__ENDPOINT_URL__', 'g'), settings.endpointUrl);
    template = template.replace(new RegExp('__EXIT_URL__', 'g'), settings.exitUrl);
    template = template.replace(new RegExp('__BANNER_WIDTH__', 'g'), settings.bannerWidth);
    template = template.replace(new RegExp('__BANNER_HEIGHT__', 'g'), settings.bannerHeight);
    template = template.replace(new RegExp('__BANNER_SIZE_STRING__', 'g'), bannerSizeString);
    
    template = template.replace('__FONT_IMPORT_LINKS__', fontImportLinks);
    template = template.replace(new RegExp('__FONT_FAMILY__', 'g'), finalFontFamily);
    
    template = template.replace(new RegExp('__BACKGROUND_COLOR__', 'g'), settings.backgroundColor);
    template = template.replace('__ACTIVE_QUESTION__', questionNumber);
    template = template.replace('__MANUAL_SIZING_CLASS__', manualSizingClass);
    template = template.replace('__MANUAL_SIZING_VARS__', manualSizingVars);
    template = template.replace(new RegExp('__THANK_YOU_TITLE__', 'g'), settings.thankYouTitle);
    template = template.replace(new RegExp('__THANK_YOU_MESSAGE__', 'g'), settings.thankYouMessage);
    template = template.replace(new RegExp('__WELCOME_TITLE__', 'g'), settings.welcomeTitle);
    template = template.replace(new RegExp('__WELCOME_BUTTON_TEXT__', 'g'), settings.welcomeButtonText);
    template = template.replace('__QUESTION_NUMBERING__', settings.questionNumbering);
    template = template.replace('__ANSWER_HOVER_COLOR__', settings.answerHoverColor);

    template = template.replace(new RegExp('__HEADER_BG_COLOR__', 'g'), settings.headerBgColor);
    template = template.replace(new RegExp('__HEADER_FONT_COLOR__', 'g'), settings.headerFontColor);
    template = template.replace(new RegExp('__QUESTION_FONT_COLOR__', 'g'), settings.questionFontColor);
    template = template.replace(new RegExp('__ANSWER_FONT_COLOR__', 'g'), settings.answerFontColor);
    template = template.replace(new RegExp('__RADIO_BG_COLOR__', 'g'), settings.radioBgColor);
    template = template.replace(new RegExp('__RADIO_BORDER_COLOR__', 'g'), settings.radioBorderColor);
    template = template.replace(new RegExp('__RADIO_DOT_COLOR__', 'g'), settings.radioDotColor);

    return template;
}

/**
 * Generuje HTML tylko z ekranem końcowym (do podglądu)
 */
function generateBannerThankYouOnly() {
    // Ta funkcja jest używana TYLKO do podglądu, więc zawsze używa
    // logiki linkowania do CDN (Metoda 2), nigdy osadzania.
    const { settings } = collectDataFromUI();
    
    let manualSizingClass = '';
    let manualSizingVars = '';
    
    if (settings.manualSizing) {
        manualSizingClass = ' manual-sizing';
        manualSizingVars = `
            --manual-thankyou-title: ${settings.thankYouTitleSize}px;
            --manual-thankyou-message: ${settings.thankYouMessageSize}px;
            --manual-padding: ${settings.paddingSize}px;
            --manual-header-font: ${settings.headerTextSize}px;
            --manual-header-padding: ${settings.headerPadding}px;
            --manual-content-top-padding: ${settings.contentTopPadding}px;
            --manual-global-question-answer-gap: ${settings.globalQuestionAnswerGap}px;
        `.trim();
    }

    let fontImportLinks = '';
    let finalFontFamily = settings.fontFamily; 

    if (settings.fontPackageName) {
        const url400 = `https://cdn.jsdelivr.net/npm/@fontsource/${settings.fontPackageName}@5/400.min.css`;
        const url700 = `https://cdn.jsdelivr.net/npm/@fontsource/${settings.fontPackageName}@5/700.min.css`;
        
        fontImportLinks = `
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="stylesheet" href="${url400}">
    <link rel="stylesheet" href="${url700}">`;
    }

    return `<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    ${fontImportLinks}
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body, html {
            width: ${settings.bannerWidth}px;
            height: ${settings.bannerHeight}px;
            overflow: hidden;
            font-family: ${finalFontFamily};
        }
        :root {
            --header-bg-color: ${settings.headerBgColor};
            --header-font-color: ${settings.headerFontColor};
            --background-color: ${settings.backgroundColor};
            --question-font-color: ${settings.questionFontColor};
            --answer-font-color: ${settings.answerFontColor};
        }
        .size-300x250 { --question-font: 16px; --answer-font: 13px; --radio-size: 16px; --spacing: 10px; --padding: 15px; --header-font: 10px; --header-padding: 6px; --thankyou-title: 20px; --thankyou-message: 13px; --content-top-padding: 15px; --global-question-answer-gap: 5px; }
        .size-300x600 { --question-font: 20px; --answer-font: 16px; --radio-size: 20px; --spacing: 14px; --padding: 20px; --header-font: 12px; --header-padding: 8px; --thankyou-title: 26px; --thankyou-message: 16px; --content-top-padding: 20px; --global-question-answer-gap: 5px; }
        .size-320x480 { --question-font: 18px; --answer-font: 15px; --radio-size: 18px; --spacing: 12px; --padding: 18px; --header-font: 11px; --header-padding: 7px; --thankyou-title: 23px; --thankyou-message: 15px; --content-top-padding: 18px; --global-question-answer-gap: 5px; }

        .manual-sizing {
            --thankyou-title: var(--manual-thankyou-title);
            --thankyou-message: var(--manual-thankyou-message);
            --padding: var(--manual-padding);
            --header-font: var(--manual-header-font);
            --header-padding: var(--manual-header-padding);
            --content-top-padding: var(--manual-content-top-padding);
            --global-question-answer-gap: var(--manual-global-question-answer-gap);
        }
        #banner-container { position: relative; width: 100%; height: 100%; display: flex; flex-direction: column; cursor: pointer; }
        .survey-header { background-color: var(--header-bg-color); color: var(--header-font-color); text-align: left; font-weight: bold; padding: var(--header-padding) var(--padding); font-size: var(--header-font); letter-spacing: 1px; flex-shrink: 0; }
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding-left: var(--padding);
            padding-right: var(--padding);
            padding-bottom: var(--padding);
            padding-top: var(--content-top-padding);
            overflow: hidden;
        }
        .thank-you-screen { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center; }
        .thank-you-title { 
            font-size: var(--thankyou-title); 
            font-weight: bold; 
            color: var(--question-font-color); 
            margin-bottom: 20px; 
        }
        .thank-you-message { 
            font-size: var(--thankyou-message); 
            color: var(--answer-font-color); 
            line-height: 1.6; 
        }
    </style>
</head>
<body style="background-color: transparent;">
    <div id="banner-container" class="size-${settings.bannerWidth}x${settings.bannerHeight}${manualSizingClass}" style="${manualSizingVars}; background-color: var(--background-color);">
        <div class="survey-header">BADANIE ANKIETOWE</div>
        <div class="content-area">
            <div class="thank-you-screen" style="display: flex;">
                <div class="thank-you-title">${settings.thankYouTitle}</div>
                <div class="thank-you-message">${settings.thankYouMessage}</div>
            </div>
        </div>
    </div>
</body>
</html>`;
}