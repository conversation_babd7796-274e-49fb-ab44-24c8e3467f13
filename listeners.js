// listeners.js - Centralna obsługa zdarzeń

import { controls, previewUpdatingFields } from './config.js';
import { 
    addQuestion, 
    removeQuestion, 
    addAnswer, 
    removeAnswer, 
    toggleManualSizing,
    updateSliderValue,
    toggleConditionalLogic,
    populateConditionalAnswers,
    autoResizeTextarea,
    moveQuestionUp,
    moveQuestionDown,
    renumberQuestions
} from './ui.js';
import { parseQuestionsFromText, populateUi, collectDataFromUI } from './data.js';
// ZMIANA: Importujemy nową funkcję `generateSelfContainedBanner` ORAZ `updatePreview`
import { generateSelfContainedBanner, updatePreview } from './preview.js';

/**
 * Ustawia podstawowe listenery dla kontrolek i pól
 */
export function setupCoreListeners(updatePreviewCallback) { // Zmieniono nazwę na "updatePreviewCallback" dla jasności
    
    previewUpdatingFields.forEach(field => {
        if (!field) return;
        // ZMIANA: 'input' dla wszystkich pól oprócz 'select' dla lepszej responsywności
        const eventType = (field.tagName === 'SELECT') ? 'change' : 'input';
        field.addEventListener(eventType, updatePreviewCallback);
    });

    // Listener dla globalnych suwaków, aby aktualizować ich wartości tekstowe
    document.querySelectorAll('#controls-panel input[type="range"]').forEach(slider => {
        slider.addEventListener('input', (e) => {
            const sliderId = e.target.id;
            updateSliderValue(sliderId, e.target.value);
        });
    });

    controls.manualSizing.addEventListener('change', () => {
        toggleManualSizing();
        updatePreviewCallback();
    });

    controls.questionsContainer.addEventListener('click', (e) => {
        const target = e.target;
        if (target.classList.contains('remove-question-btn')) removeQuestion(target);
        if (target.classList.contains('move-question-up-btn')) moveQuestionUp(target);
        if (target.classList.contains('move-question-down-btn')) moveQuestionDown(target);
        if (target.classList.contains('add-answer-btn')) addAnswer(target);
        if (target.classList.contains('remove-answer-btn')) removeAnswer(target);
    });

    // Listener dla inputów wewnątrz pytań
    controls.questionsContainer.addEventListener('input', (e) => {
        const target = e.target;
        
        if (target.classList.contains('question-text')) {
            autoResizeTextarea(target);
        }

        // NOWOŚĆ: Listenery dla suwaków wewnątrz karty pytania
        if (target.classList.contains('question-answer-gap-slider')) {
            const valueSpan = target.closest('.brutalist-inner-card').querySelector('.question-answer-gap-value');
            if (valueSpan) valueSpan.textContent = `${target.value}px`;
            updatePreviewCallback();
        }
        
        if (target.classList.contains('answer-spacing-slider')) {
            const valueSpan = target.closest('.brutalist-inner-card').querySelector('.answer-spacing-value');
            if (valueSpan) valueSpan.textContent = `${target.value}px`;
            updatePreviewCallback();
        }
        
        // NOWOŚĆ: Listener dla suwaka proporcji kolumn
        if (target.classList.contains('column-width-slider')) {
            const value = target.value;
            const valueSpan = target.closest('.column-width-slider-container').querySelector('.column-width-value');
            if (valueSpan) valueSpan.textContent = `${value}% / ${100 - value}%`;
            updatePreviewCallback();
        }
        // KONIEC NOWOŚCI

        if (target.classList.contains('randomize-answers') ||
           target.classList.contains('conditional-answer-ref') ||
           target.classList.contains('answer-text')) {
            updatePreviewCallback();
        }
    });

    // Listener dla zmian (selecty) wewnątrz pytań
    controls.questionsContainer.addEventListener('change', (e) => {
        const target = e.target;

        if(target.classList.contains('conditional-enabled')) {
            toggleConditionalLogic(target);
        }
        
        if(target.classList.contains('conditional-question-ref')) {
            populateConditionalAnswers(target);
            updatePreviewCallback();
        }

        if(target.classList.contains('answers-layout')) {
            // NOWOŚĆ: Pokaż/ukryj suwak proporcji kolumn
            const questionCard = target.closest('[data-question-id]');
            const sliderContainer = questionCard.querySelector('.column-width-slider-container');
            if (target.value === '2-col') {
                sliderContainer.style.display = 'block';
            } else {
                sliderContainer.style.display = 'none';
            }
            // KONIEC NOWOŚCI
            updatePreviewCallback();
        }
    });
    
    // Synchronizacja dla pól koloru
    setupColorSync(controls.backgroundColor, controls.backgroundColorText, updatePreviewCallback);
    setupColorSync(controls.answerHoverColor, controls.answerHoverColorText, updatePreviewCallback);
    setupColorSync(controls.headerBgColor, controls.headerBgColorText, updatePreviewCallback);
    setupColorSync(controls.headerFontColor, controls.headerFontColorText, updatePreviewCallback);
    setupColorSync(controls.questionFontColor, controls.questionFontColorText, updatePreviewCallback);
    setupColorSync(controls.answerFontColor, controls.answerFontColorText, updatePreviewCallback);
    setupColorSync(controls.radioBgColor, controls.radioBgColorText, updatePreviewCallback);
    setupColorSync(controls.radioBorderColor, controls.radioBorderColorText, updatePreviewCallback);
    setupColorSync(controls.radioDotColor, controls.radioDotColorText, updatePreviewCallback);

    controls.addQuestionBtn.addEventListener('click', () => {
        addQuestion();
        updatePreviewCallback();
    });

    // --- LOGIKA DRAG-AND-DROP (Z OBSŁUGĄ DOTYKU) ---
    let isDragging = false;
    let draggedElement = null;
    let placeholder = document.createElement('div');
    placeholder.className = 'drag-placeholder';
    let offsetY = 0; 
    let offsetX = 0;
    let startY = 0; 

    function collapseAllQuestions(collapse = true, exception = null) {
        const allCards = controls.questionsContainer.querySelectorAll('.brutalist-card');
        allCards.forEach(card => {
            if (card === exception) return;
            if (collapse) card.classList.add('collapsed');
            else card.classList.remove('collapsed');
        });
    }

    function handleDragStart(e) {
        const handle = e.target.closest('.question-drag-handle');
        if (!handle || e.target.closest('button, input, select, textarea')) return;

        if (e.type === 'touchstart') {
            e.preventDefault();
        }

        document.body.classList.add('is-dragging');

        isDragging = true;
        draggedElement = handle.closest('.brutalist-card');
        collapseAllQuestions(true, draggedElement);
        draggedElement.classList.add('collapsed');

        const rect = draggedElement.getBoundingClientRect();
        const handleRect = handle.getBoundingClientRect();
        
        const mainPanel = document.querySelector('main');
        const mainRect = mainPanel.getBoundingClientRect();
        
        const clientY = e.clientY || e.touches[0].clientY;
        const clientX = e.clientX || e.touches[0].clientX;
        
        startY = clientY;
        offsetY = clientY - rect.top;
        offsetX = clientX - rect.left;

        draggedElement.classList.add('dragging');
        draggedElement.style.position = 'absolute';
        draggedElement.style.width = `${rect.width}px`;
        
        draggedElement.style.top = `${rect.top - mainRect.top}px`; 
        draggedElement.style.left = `${rect.left - mainRect.left}px`;
        
        draggedElement.style.zIndex = '1000';
        handle.style.cursor = 'grabbing';

        placeholder.style.height = `${handleRect.height}px`;
        draggedElement.parentNode.insertBefore(placeholder, draggedElement);
        
        if (e.type === 'mousedown') {
            document.addEventListener('mousemove', onDragMove);
            document.addEventListener('mouseup', onDragEnd);
        } else if (e.type === 'touchstart') {
            document.addEventListener('touchmove', onDragMove, { passive: false }); 
            document.addEventListener('touchend', onDragEnd);
        }
    }

    controls.questionsContainer.addEventListener('mousedown', handleDragStart);
    controls.questionsContainer.addEventListener('touchstart', handleDragStart, { passive: false }); 


    function onDragMove(e) {
        if (!isDragging) return;

        if (e.type === 'touchmove') {
            e.preventDefault();
        }

        const mainPanel = document.querySelector('main');
        const mainRect = mainPanel.getBoundingClientRect();
                
        const clientY = e.clientY || e.touches[0].clientY;
        const clientX = e.clientX || e.touches[0].clientX;
        
        if (clientY === undefined) return;

        const newTop = clientY - mainRect.top - offsetY;
        const newLeft = clientX - mainRect.left - offsetX;

        draggedElement.style.top = `${newTop}px`;
        draggedElement.style.left = `${newLeft}px`;

        const container = controls.questionsContainer;
        const elements = Array.from(container.children).filter(c => c === placeholder || (c.classList.contains('brutalist-card') && !c.classList.contains('dragging')));
        let closest = null;
        let minDistance = Infinity;

        for (const el of elements) {
            const rect = el.getBoundingClientRect();
            const midY = rect.top + rect.height / 2;
            const distance = Math.abs(clientY - midY);
            if (distance < minDistance) {
                minDistance = distance;
                closest = el;
            }
        }

        if (closest) {
            const rect = closest.getBoundingClientRect();
            const midY = rect.top + rect.height / 2;
            if (clientY > midY) {
                closest.parentNode.insertBefore(placeholder, closest.nextSibling);
            } else {
                closest.parentNode.insertBefore(placeholder, closest);
            }
            placeholder.style.height = '60px';
        }
    }

    function onDragEnd() {
        if (!isDragging) return;
        
        document.body.classList.remove('is-dragging');
        
        isDragging = false;

        placeholder.parentNode.insertBefore(draggedElement, placeholder);
        placeholder.remove();

        const handle = draggedElement.querySelector('.question-drag-handle');
        if (handle) handle.style.cursor = 'grab';

        draggedElement.classList.remove('dragging');
        draggedElement.style.position = 'static';
        draggedElement.style.width = 'auto';
        draggedElement.style.top = 'auto';
        draggedElement.style.left = 'auto';
        draggedElement.style.zIndex = 'auto';
        draggedElement = null;

        document.removeEventListener('mousemove', onDragMove);
        document.removeEventListener('mouseup', onDragEnd);
        document.removeEventListener('touchmove', onDragMove);
        document.removeEventListener('touchend', onDragEnd);
        
        collapseAllQuestions(false);
        renumberQuestions();
        updatePreviewCallback(); // Użyj przekazanej funkcji
    }
}

function setupColorSync(colorInput, textInput, updateCallback) {
    colorInput.addEventListener('input', () => {
        textInput.value = colorInput.value;
        updateCallback();
    });
    textInput.addEventListener('input', () => {
        if (/^#[0-9A-F]{6}$/i.test(textInput.value)) {
            colorInput.value = textInput.value;
            updateCallback();
        }
    });
}

export function setupImportListeners(updatePreviewCallback) { // Zmieniono nazwę
    controls.chooseFileBtn.addEventListener('click', () => {
        controls.questionsFileInput.click();
    });

    controls.questionsFileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (e) => {
            controls.questionsTextInput.value = e.target.result;
        };
        reader.readAsText(file, 'UTF-8');
    });

    controls.importQuestionsBtn.addEventListener('click', () => {
        const text = controls.questionsTextInput.value;
        if (!text.trim()) return;
        
        const parsedQuestions = parseQuestionsFromText(text);
        if (parsedQuestions.length === 0) return;
        
        controls.questionsContainer.innerHTML = '';
        
        parsedQuestions.forEach(q => {
            addQuestion(q.text, q.answers, false, null, '1-col');
        });
        
        controls.questionsTextInput.value = '';
        updatePreviewCallback(); // Użyj przekazanej funkcji
    });
}

/**
 * NOWOŚĆ: Zaktualizowana funkcja generatora
 */
export function setupGeneratorListeners() {
    const loadingOverlay = document.getElementById('loading-overlay');
    
    // ZMIANA: Funkcja jest teraz asynchroniczna
    controls.generateBtn.addEventListener('click', async () => {
        
        // Pokaż ekran ładowania
        loadingOverlay.classList.add('show');
        
        try {
            // 1. Pobierz dane UI (potrzebne do nazwy pliku i sprawdzenia, czy są pytania)
            const { settings, surveyData } = collectDataFromUI();

            if (surveyData.questions.length === 0) {
                // Ukryj spinner przed pokazaniem alertu
                loadingOverlay.classList.remove('show');
                // Użyj 'alert' zamiast 'window.alert', chociaż w tym kontekście to to samo
                alert("Nie można wygenerować banera. Dodaj przynajmniej jedno pytanie.");
                return; // Zakończ wcześniej
            }

            // 2. Wywołaj nową, asynchroniczną funkcję, która pobiera i osadza fonty
            const bannerHtml = await generateSelfContainedBanner();

            if (!bannerHtml) {
                throw new Error("Nie udało się wygenerować kodu HTML banera.");
            }
            
            // 3. Stwórz i pobierz plik blob (jak wcześniej)
            const blob = new Blob([bannerHtml], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `banner_${settings.bannerWidth}x${settings.bannerHeight}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

        } catch (error) {
            console.error("Błąd podczas generowania samowystarczalnego banera:", error);
            alert(`Wystąpił błąd podczas generowania banera: ${error.message}`);
            
        } finally {
            // Zawsze ukryj ekran ładowania
            loadingOverlay.classList.remove('show');
        }
    });
}


export function setupLoadListeners(updatePreviewCallback) { // Zmieniono nazwę
    controls.loadBannerBtn.addEventListener('click', () => {
            controls.loadBannerInput.click();
    });

    controls.loadBannerInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try { // NOWOŚĆ: Dodano try...catch do parsowania
                const htmlContent = e.target.result;
                const parser = new DOMParser();
                const doc = parser.parseFromString(htmlContent, 'text/html');
                
                const allScripts = Array.from(doc.querySelectorAll('script'));
                const dataScript = allScripts.find(s => s.textContent.includes('const surveyData ='));

                if (!dataScript) {
                    console.error("Nie znaleziono skryptu z danymi ankiety (surveyData) w ładowanym pliku.");
                    alert("Nie udało się wczytać pliku. Prawdopodobnie nie jest to baner wygenerowany przez ten system.");
                    return;
                }
                
                const scriptContent = dataScript.textContent;

                const surveyDataMatch = scriptContent.match(/const surveyData = ({[\s\S]*?});/);
                const endpointMatch = scriptContent.match(/const endpointUrl = "(.+?)";/);
                
                if (!surveyDataMatch) {
                    alert("Nie udało się odczytać danych ankiety z pliku.");
                    return;
                }

                const surveyData = JSON.parse(surveyDataMatch[1]);
                const endpointUrl = endpointMatch ? endpointMatch[1] : '';

                const newClickTagMatch = htmlContent.match(/var clickTag = "(.+?)";/);
                const oldExitUrlMatch = scriptContent.match(/const exitUrl = "(.+?)";/);

                let exitUrl = ''; 
                
                if (newClickTagMatch && newClickTagMatch[1]) {
                    exitUrl = newClickTagMatch[1];
                } else if (oldExitUrlMatch && oldExitUrlMatch[1]) {
                    exitUrl = oldExitUrlMatch[1];
                }
                
                const metaTag = doc.querySelector('meta[name="ad.size"]');
                let bannerWidth = 300, bannerHeight = 250;
                if (metaTag) {
                    const content = metaTag.getAttribute('content');
                    const match = content.match(/width=(\d+),height=(\d+)/);
                    if (match) {
                        bannerWidth = match[1];
                        bannerHeight = match[2];
                    }
                }

                // --- NOWA LOGIKA CZYTANIA FONTÓW ---
                let loadedFontPackageName = '';
                let fontFamily = 'Arial, sans-serif'; 
                const styleTags = Array.from(doc.querySelectorAll('style'));
                const styleContent = styleTags.map(s => s.textContent).join('\n');
                
                if (styleContent) {
                    const fontFaceMatch = styleContent.match(/@font-face\s*\{\s*font-family:\s*['"](.*?)['"];/);
                    
                    if (fontFaceMatch && fontFaceMatch[1]) {
                        // SCENARIUSZ 1: Znaleziono osadzony font
                        const fontCSSName = fontFaceMatch[1];
                        fontFamily = `'${fontCSSName}', Arial, sans-serif`; 
                        
                        const knownFonts = {
                            "Lato": "lato",
                            "Roboto": "roboto",
                            "Open Sans": "open-sans",
                            "Montserrat": "montserrat",
                            "Oswald": "oswald",
                            "Source Sans Pro": "source-sans-pro"
                        };
                        if (knownFonts[fontCSSName]) {
                            loadedFontPackageName = knownFonts[fontCSSName];
                        }

                    } else {
                        // SCENARIUSZ 2: Sprawdź linki (stare banery)
                        const fontLinks = Array.from(doc.querySelectorAll('link[href*="@fontsource"]'));
                        if (fontLinks.length > 0) {
                            const href = fontLinks[0].getAttribute('href');
                            const fontMatch = href.match(/@fontsource\/(.*?)@/); 
                            if (fontMatch && fontMatch[1]) {
                                loadedFontPackageName = fontMatch[1];
                            }
                        }

                        // SCENARIUSZ 3: Odczytaj font-family z body
                        let fontMatch = styleContent.match(/body, html {\s*font-family:\s*([^;]+);/);
                        if (fontMatch && fontMatch[1]) {
                            fontFamily = fontMatch[1].trim();
                        }
                    }
                }
                
                let answerHoverColor = '#f1f5f9';
                let backgroundColor = '#ffffff';
                let headerBgColor = '#000000';
                let headerFontColor = '#ffffff';
                let questionFontColor = '#1e293b';
                let answerFontColor = '#334155';
                let radioBgColor = '#ffffff';
                let radioBorderColor = '#64748b';
                let radioDotColor = '#3b82f6';

                if (styleContent) {
                    const hoverMatch = styleContent.match(/--answer-hover-color:\s*([^;]+);/);
                    if (hoverMatch) answerHoverColor = hoverMatch[1].trim();
                    const bgMatch = styleContent.match(/--background-color:\s*([^;]+);/);
                    if (bgMatch) backgroundColor = bgMatch[1].trim();
                    const headerBgMatch = styleContent.match(/--header-bg-color:\s*([^;]+);/);
                    if (headerBgMatch) headerBgColor = headerBgMatch[1].trim();
                    const headerFontMatch = styleContent.match(/--header-font-color:\s*([^;]+);/);
                    if (headerFontMatch) headerFontColor = headerFontMatch[1].trim();
                    const questionFontMatch = styleContent.match(/--question-font-color:\s*([^;]+);/);
                    if (questionFontMatch) questionFontColor = questionFontMatch[1].trim();
                    const answerFontMatch = styleContent.match(/--answer-font-color:\s*([^;]+);/);
                    if (answerFontMatch) answerFontColor = answerFontMatch[1].trim();
                    const radioBgMatch = styleContent.match(/--radio-bg-color:\s*([^;]+);/);
                    if (radioBgMatch) radioBgColor = radioBgMatch[1].trim();
                    const radioBorderMatch = styleContent.match(/--radio-border-color:\s*([^;]+);/);
                    if (radioBorderMatch) radioBorderColor = radioBorderMatch[1].trim();
                    const radioDotMatch = styleContent.match(/--radio-dot-color:\s*([^;]+);/);
                    if (radioDotMatch) radioDotColor = radioDotMatch[1].trim();
                }
                // --- KONIEC LOGIKI CZYTANIA ---

                const bannerContainer = doc.getElementById('banner-container');
                const isManual = bannerContainer ? bannerContainer.classList.contains('manual-sizing') : false;
                let manualSettings = { manualSizing: isManual };

                if (isManual && bannerContainer) {
                    const manualStyles = bannerContainer.getAttribute('style');
                    const styleMap = {};
                    
                    if(manualStyles) {
                        manualStyles.split(';').forEach(style => {
                            const parts = style.split(':');
                            if (parts.length === 2) {
                                const key = parts[0].trim();
                                const value = parts[1].trim().replace('px', '');
                                styleMap[key] = value;
                            }
                        });
                    }

                    manualSettings.headerTextSize = styleMap['--manual-header-font'];
                    manualSettings.headerPadding = styleMap['--manual-header-padding'];
                    manualSettings.questionFontSize = styleMap['--manual-question-font'];
                    manualSettings.answerFontSize = styleMap['--manual-answer-font'];
                    manualSettings.radioSize = styleMap['--manual-radio-size'];
                    manualSettings.spacingSize = styleMap['--manual-spacing'];
                    manualSettings.paddingSize = styleMap['--manual-padding'];
                    manualSettings.contentTopPadding = styleMap['--manual-content-top-padding'];
                    manualSettings.globalQuestionAnswerGap = styleMap['--manual-global-question-answer-gap'];
                    manualSettings.thankYouTitleSize = styleMap['--manual-thankyou-title'];
                    manualSettings.thankYouMessageSize = styleMap['--manual-thankyou-message'];
                }
                
                const thankYouTitle = doc.querySelector('.thank-you-title')?.textContent;
                const thankYouMessage = doc.querySelector('.thank-you-message')?.textContent;

                const settings = { 
                    endpointUrl, exitUrl, bannerWidth, bannerHeight, 
                    fontPackageName: loadedFontPackageName, 
                    fontFamily, 
                    backgroundColor, answerHoverColor,
                    headerBgColor, headerFontColor, questionFontColor, answerFontColor,
                    radioBgColor, radioBorderColor, radioDotColor,
                    ...manualSettings, 
                    thankYouTitle,
                    thankYouMessage
                };
                
                controls.questionsContainer.innerHTML = ''; 
                populateUi(settings, surveyData);

            } catch (error) {
                console.error("Błąd podczas parsowania wczytanego pliku:", error);
                alert(`Wystąpił błąd podczas wczytywania pliku: ${error.message}`);
            }
        };
        reader.readAsText(file);
    });
}